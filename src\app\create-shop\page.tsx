'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { 
  Store, 
  Upload, 
  Globe, 
  Mail, 
  Phone, 
  MapPin,
  Save,
  ArrowLeft
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { VendorShopService } from '@/lib/services/vendorShops'
import { KYCService } from '@/lib/services/kyc'
import { useKYCCheck } from '@/hooks/useKYCVerification'
import { supabase } from '@/lib/supabase'

export default function CreateShopPage() {
  const router = useRouter()
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const { requireKYCForAction } = useKYCCheck()
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    contact_email: '',
    contact_phone: '',
    address: '',
    website_url: '',
    social_links: {
      facebook: '',
      instagram: '',
      twitter: ''
    }
  })

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      router.push('/auth/signin')
      return
    }

    // Check if user already has a shop
    try {
      const existingShop = await VendorShopService.getUserShop(user.id)
      if (existingShop) {
        alert('You already have a shop. You can only create one shop per account.')
        router.push('/dashboard/shop')
        return
      }
    } catch (error) {
      console.error('Error checking existing shop:', error)
    }

    setUser(user)
  }

  const handleInputChange = (field: string, value: string) => {
    if (field.startsWith('social_links.')) {
      const socialField = field.split('.')[1]
      setFormData(prev => ({
        ...prev,
        social_links: {
          ...prev.social_links,
          [socialField]: value
        }
      }))
    } else {
      setFormData(prev => ({ ...prev, [field]: value }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    // Check KYC verification status
    const canCreateShop = await requireKYCForAction('shop_creation')
    if (!canCreateShop) {
      alert('KYC Verification Required: You must complete KYC verification before creating a shop. Please go to Profile > Identity Verification to submit your documents.')
      return
    }

    setLoading(true)
    try {
      const shopData = {
        user_id: user.id,
        name: formData.name,
        slug: '', // Will be generated by the service
        description: formData.description,
        contact_email: formData.contact_email || user.email,
        contact_phone: formData.contact_phone,
        address: formData.address,
        website_url: formData.website_url,
        social_links: formData.social_links,
        status: 'pending' as const,
        is_featured: false,
        total_views: 0
      }

      const shop = await VendorShopService.createShop(shopData)

      alert('Shop created successfully! It will be reviewed by our team before going live.')
      router.push(`/shop/${shop.slug}`)
    } catch (error) {
      console.error('Error creating shop:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to create shop. Please try again.'
      alert(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="py-8 pt-28">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="mb-8">
            <button
              onClick={() => router.back()}
              className="flex items-center text-gray-600 hover:text-gray-900 mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </button>
            <div className="text-center">
              <div className="mx-auto w-16 h-16 bg-primary-blue/10 rounded-full flex items-center justify-center mb-4">
                <Store className="h-8 w-8 text-primary-blue" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Create Your Shop</h1>
              <p className="text-gray-600">
                Start selling your products on OKDOI marketplace
              </p>
            </div>
          </div>

          {/* Form */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Shop Name *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                      placeholder="Enter your shop name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      rows={4}
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                      placeholder="Tell customers about your shop and products"
                    />
                  </div>
                </div>
              </div>

              {/* Contact Information */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <Mail className="h-4 w-4 inline mr-1" />
                      Contact Email
                    </label>
                    <input
                      type="email"
                      value={formData.contact_email}
                      onChange={(e) => handleInputChange('contact_email', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                      placeholder={user.email}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <Phone className="h-4 w-4 inline mr-1" />
                      Contact Phone
                    </label>
                    <input
                      type="tel"
                      value={formData.contact_phone}
                      onChange={(e) => handleInputChange('contact_phone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                      placeholder="Your phone number"
                    />
                  </div>
                </div>
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <MapPin className="h-4 w-4 inline mr-1" />
                    Business Address
                  </label>
                  <textarea
                    rows={2}
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    placeholder="Your business address"
                  />
                </div>
              </div>

              {/* Online Presence */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Online Presence</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <Globe className="h-4 w-4 inline mr-1" />
                      Website URL
                    </label>
                    <input
                      type="url"
                      value={formData.website_url}
                      onChange={(e) => handleInputChange('website_url', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                      placeholder="https://yourwebsite.com"
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Facebook
                      </label>
                      <input
                        type="url"
                        value={formData.social_links.facebook}
                        onChange={(e) => handleInputChange('social_links.facebook', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        placeholder="Facebook page URL"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Instagram
                      </label>
                      <input
                        type="url"
                        value={formData.social_links.instagram}
                        onChange={(e) => handleInputChange('social_links.instagram', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        placeholder="Instagram profile URL"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Twitter
                      </label>
                      <input
                        type="url"
                        value={formData.social_links.twitter}
                        onChange={(e) => handleInputChange('social_links.twitter', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                        placeholder="Twitter profile URL"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Terms */}
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Review Process</h4>
                <p className="text-sm text-gray-600">
                  Your shop will be reviewed by our team before going live. This usually takes 1-2 business days. 
                  You'll receive an email notification once your shop is approved.
                </p>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={loading || !formData.name}
                  className="flex items-center px-6 py-3 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {loading ? 'Creating Shop...' : 'Create Shop'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
