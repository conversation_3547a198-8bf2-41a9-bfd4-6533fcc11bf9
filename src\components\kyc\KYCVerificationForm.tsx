'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Shield,
  Upload,
  AlertCircle,
  CheckCircle,
  Loader2,
  FileText,
  Calendar,
  MapPin,
  User,
  CreditCard
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { KYCService, KYCSubmission, KYCSubmissionData, KYCDocuments } from '@/lib/services/kyc'
import { KYCStatusCard, KYCProgress } from './KYCStatusBadge'
import KYCDocumentUpload from './KYCDocumentUpload'
import Input from '@/components/ui/Input'
import { PremiumButton, PremiumCard, FadeIn, SlideInUp } from '@/components/ui/premium'

interface KYCVerificationFormProps {
  onSubmissionComplete?: () => void
}

export default function KYCVerificationForm({ onSubmissionComplete }: KYCVerificationFormProps) {
  const { user } = useAuth()
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // KYC Status
  const [kycStatus, setKycStatus] = useState<{
    kyc_status: string
    kyc_submitted_at?: string
    kyc_approved_at?: string
  } | null>(null)
  const [kycSubmission, setKycSubmission] = useState<KYCSubmission | null>(null)

  // Form Data
  const [formData, setFormData] = useState<KYCSubmissionData>({
    id_document_type: 'national_id',
    id_document_number: '',
    full_name: user?.full_name || '',
    date_of_birth: '',
    address: '',
    submission_notes: ''
  })

  // Document Files
  const [documents, setDocuments] = useState<Partial<KYCDocuments>>({})
  const [documentErrors, setDocumentErrors] = useState<Record<string, string>>({})

  // Document Types
  const [documentTypes, setDocumentTypes] = useState<Array<{
    id: string
    name: string
    description: string
  }>>([])

  useEffect(() => {
    if (user) {
      fetchKYCData()
      fetchDocumentTypes()
    }
  }, [user])

  const fetchKYCData = async () => {
    if (!user) return

    try {
      setLoading(true)
      const [status, submission] = await Promise.all([
        KYCService.getUserKYCStatus(user.id),
        KYCService.getUserKYCSubmission(user.id)
      ])

      setKycStatus(status)
      setKycSubmission(submission)

      // Pre-fill form with existing submission data
      if (submission) {
        setFormData({
          id_document_type: submission.id_document_type,
          id_document_number: submission.id_document_number || '',
          full_name: submission.full_name,
          date_of_birth: submission.date_of_birth || '',
          address: submission.address,
          submission_notes: submission.submission_notes || ''
        })
      }
    } catch (error) {
      console.error('Error fetching KYC data:', error)
      setError('Failed to load KYC information')
    } finally {
      setLoading(false)
    }
  }

  const fetchDocumentTypes = async () => {
    try {
      const types = await KYCService.getKYCDocumentTypes()
      setDocumentTypes(types)
    } catch (error) {
      console.error('Error fetching document types:', error)
    }
  }

  const handleInputChange = (field: keyof KYCSubmissionData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setError('')
  }

  const handleDocumentSelect = (documentType: keyof KYCDocuments, file: File | null) => {
    if (file) {
      setDocuments(prev => ({ ...prev, [documentType]: file }))
      setDocumentErrors(prev => ({ ...prev, [documentType]: '' }))
    } else {
      setDocuments(prev => {
        const newDocs = { ...prev }
        delete newDocs[documentType]
        return newDocs
      })
    }
  }

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}
    let isValid = true

    // Validate required fields
    if (!formData.full_name.trim()) {
      setError('Full name is required')
      isValid = false
    }

    if (!formData.address.trim()) {
      setError('Address is required')
      isValid = false
    }

    // Validate required documents
    const requiredDocs: (keyof KYCDocuments)[] = ['id_front', 'id_back', 'selfie', 'address_proof']
    
    for (const docType of requiredDocs) {
      if (!documents[docType]) {
        errors[docType] = 'This document is required'
        isValid = false
      }
    }

    setDocumentErrors(errors)

    if (!isValid && !error) {
      setError('Please fill in all required fields and upload all required documents')
    }

    return isValid
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user || !validateForm()) return

    try {
      setSubmitting(true)
      setError('')

      const kycDocuments: KYCDocuments = {
        id_front: documents.id_front!,
        id_back: documents.id_back!,
        selfie: documents.selfie!,
        address_proof: documents.address_proof!
      }

      await KYCService.submitKYCApplication(user.id, formData, kycDocuments)

      setSuccess('KYC application submitted successfully! We will review your documents and notify you of the result.')
      
      // Refresh KYC data
      await fetchKYCData()
      
      if (onSubmissionComplete) {
        onSubmissionComplete()
      }

      // Clear form
      setDocuments({})
      setDocumentErrors({})
    } catch (error) {
      console.error('Error submitting KYC application:', error)
      setError(error instanceof Error ? error.message : 'Failed to submit KYC application')
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary-blue" />
      </div>
    )
  }

  const canSubmit = !kycSubmission || kycSubmission.status === 'rejected'
  const isVerified = kycStatus?.kyc_status === 'approved'

  return (
    <div className="space-y-6">
      {/* KYC Status Card */}
      <FadeIn>
        <KYCStatusCard
          status={kycStatus?.kyc_status as any || 'not_submitted'}
          submittedAt={kycStatus?.kyc_submitted_at}
          approvedAt={kycStatus?.kyc_approved_at}
          rejectionReason={kycSubmission?.rejection_reason}
        />
      </FadeIn>

      {/* Progress Indicator */}
      <FadeIn delay={0.1}>
        <PremiumCard className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Verification Progress</h3>
          <KYCProgress currentStatus={kycStatus?.kyc_status as any || 'not_submitted'} />
        </PremiumCard>
      </FadeIn>

      {/* KYC Form */}
      {canSubmit && (
        <SlideInUp delay={0.2}>
          <PremiumCard className="p-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-2 bg-primary-blue/10 rounded-lg">
                <Shield className="h-6 w-6 text-primary-blue" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {kycSubmission?.status === 'rejected' ? 'Resubmit KYC Documents' : 'Submit KYC Documents'}
                </h3>
                <p className="text-gray-600">
                  Complete your identity verification to unlock all features
                </p>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <Input
                    type="text"
                    value={formData.full_name}
                    onChange={(e) => handleInputChange('full_name', e.target.value)}
                    placeholder="Enter your full name as per ID"
                    required
                    icon={User}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Date of Birth
                  </label>
                  <Input
                    type="date"
                    value={formData.date_of_birth}
                    onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                    icon={Calendar}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    ID Document Type *
                  </label>
                  <select
                    value={formData.id_document_type}
                    onChange={(e) => handleInputChange('id_document_type', e.target.value as any)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                    required
                  >
                    {documentTypes.map(type => (
                      <option key={type.id} value={type.id}>
                        {type.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    ID Document Number
                  </label>
                  <Input
                    type="text"
                    value={formData.id_document_number}
                    onChange={(e) => handleInputChange('id_document_number', e.target.value)}
                    placeholder="Enter ID number"
                    icon={CreditCard}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Address *
                </label>
                <textarea
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="Enter your full address"
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  required
                />
              </div>

              {/* Document Uploads */}
              <div className="space-y-6">
                <h4 className="text-lg font-semibold text-gray-900">Required Documents</h4>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <KYCDocumentUpload
                    documentType="id_front"
                    onFileSelect={(file) => handleDocumentSelect('id_front', file)}
                    selectedFile={documents.id_front}
                    error={documentErrors.id_front}
                    disabled={submitting}
                  />
                  
                  <KYCDocumentUpload
                    documentType="id_back"
                    onFileSelect={(file) => handleDocumentSelect('id_back', file)}
                    selectedFile={documents.id_back}
                    error={documentErrors.id_back}
                    disabled={submitting}
                  />
                  
                  <KYCDocumentUpload
                    documentType="selfie"
                    onFileSelect={(file) => handleDocumentSelect('selfie', file)}
                    selectedFile={documents.selfie}
                    error={documentErrors.selfie}
                    disabled={submitting}
                  />
                  
                  <KYCDocumentUpload
                    documentType="address_proof"
                    onFileSelect={(file) => handleDocumentSelect('address_proof', file)}
                    selectedFile={documents.address_proof}
                    error={documentErrors.address_proof}
                    disabled={submitting}
                  />
                </div>
              </div>

              {/* Additional Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Notes (Optional)
                </label>
                <textarea
                  value={formData.submission_notes}
                  onChange={(e) => handleInputChange('submission_notes', e.target.value)}
                  placeholder="Any additional information you'd like to provide"
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                />
              </div>

              {/* Error/Success Messages */}
              <AnimatePresence>
                {error && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="flex items-center space-x-2 text-red-600 bg-red-50 border border-red-200 rounded-lg p-3"
                  >
                    <AlertCircle className="h-5 w-5 flex-shrink-0" />
                    <span>{error}</span>
                  </motion.div>
                )}

                {success && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="flex items-center space-x-2 text-green-600 bg-green-50 border border-green-200 rounded-lg p-3"
                  >
                    <CheckCircle className="h-5 w-5 flex-shrink-0" />
                    <span>{success}</span>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Submit Button */}
              <div className="flex justify-end">
                <PremiumButton
                  type="submit"
                  disabled={submitting}
                  className="px-8 py-3"
                >
                  {submitting ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      {kycSubmission?.status === 'rejected' ? 'Resubmit Documents' : 'Submit for Verification'}
                    </>
                  )}
                </PremiumButton>
              </div>
            </form>
          </PremiumCard>
        </SlideInUp>
      )}
    </div>
  )
}
