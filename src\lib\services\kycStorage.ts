import { supabase } from '@/lib/supabase'

export type KYCDocumentType = 'id_front' | 'id_back' | 'selfie' | 'address_proof'

export interface KYCDocumentUpload {
  file: File
  type: KYCDocumentType
  description?: string
}

export class KYCStorageService {
  private static readonly BUCKET_NAME = 'kyc-documents'
  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB for KYC documents
  private static readonly ALLOWED_TYPES = [
    'image/jpeg', 
    'image/jpg', 
    'image/png', 
    'image/webp',
    'application/pdf' // Allow PDF for address proof documents
  ]

  /**
   * Ensure KYC bucket exists before operations
   */
  private static async ensureBucketExists(): Promise<void> {
    try {
      // Check if bucket exists
      const { data: buckets, error: listError } = await supabase.storage.listBuckets()

      if (listError) {
        console.error('Error listing buckets:', listError)
        console.log('Assuming bucket exists and continuing...')
        return
      }

      const bucketExists = buckets?.some(bucket => bucket.name === this.BUCKET_NAME)

      if (!bucketExists) {
        console.log(`Creating KYC bucket: ${this.BUCKET_NAME}`)
        
        const { error: createError } = await supabase.storage.createBucket(this.BUCKET_NAME, {
          public: false, // KYC documents should be private
          allowedMimeTypes: this.ALLOWED_TYPES,
          fileSizeLimit: this.MAX_FILE_SIZE
        })

        if (createError) {
          console.error('Error creating KYC bucket:', createError)
          throw new Error(`Failed to create KYC storage bucket: ${createError.message}`)
        }

        console.log('KYC bucket created successfully')
      }
    } catch (error) {
      console.error('Error ensuring KYC bucket exists:', error)
      throw error
    }
  }

  /**
   * Validate KYC document file
   */
  private static validateFile(file: File, documentType: KYCDocumentType): void {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error(`File size must be less than ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`)
    }

    // Check file type
    if (!this.ALLOWED_TYPES.includes(file.type)) {
      throw new Error(`File type ${file.type} is not supported. Allowed types: ${this.ALLOWED_TYPES.join(', ')}`)
    }

    // Additional validation for specific document types
    if (documentType === 'selfie' && file.type === 'application/pdf') {
      throw new Error('Selfie photos must be image files, not PDF')
    }

    // Check if file name is reasonable
    if (file.name.length > 255) {
      throw new Error('File name is too long')
    }
  }

  /**
   * Generate secure filename for KYC document
   */
  private static generateSecureFileName(userId: string, documentType: KYCDocumentType, originalName: string): string {
    const timestamp = Date.now()
    const randomId = Math.random().toString(36).substring(2)
    const fileExt = originalName.split('.').pop()?.toLowerCase() || 'jpg'
    
    // Create folder structure: userId/documentType/timestamp-randomId.ext
    return `${userId}/${documentType}/${timestamp}-${randomId}.${fileExt}`
  }

  /**
   * Upload a single KYC document
   */
  static async uploadKYCDocument(
    file: File, 
    userId: string, 
    documentType: KYCDocumentType
  ): Promise<string> {
    try {
      // Ensure bucket exists
      await this.ensureBucketExists()

      // Validate file
      this.validateFile(file, documentType)

      // Generate secure filename
      const fileName = this.generateSecureFileName(userId, documentType, file.name)

      console.log(`Uploading KYC document: ${fileName} to bucket: ${this.BUCKET_NAME}`)

      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false // Don't overwrite existing files
        })

      if (error) {
        console.error('KYC document upload error:', error)
        throw new Error(`Upload failed: ${error.message}`)
      }

      console.log('KYC document upload successful:', data)

      // For private buckets, we need to create signed URLs for access
      // Return the file path for now, signed URL will be generated when needed
      return fileName
    } catch (error) {
      console.error('Error uploading KYC document:', error)
      throw error
    }
  }

  /**
   * Upload multiple KYC documents
   */
  static async uploadKYCDocuments(
    documents: KYCDocumentUpload[], 
    userId: string
  ): Promise<Record<KYCDocumentType, string>> {
    const results: Record<string, string> = {}

    try {
      // Upload all documents concurrently
      const uploadPromises = documents.map(async (doc) => {
        const filePath = await this.uploadKYCDocument(doc.file, userId, doc.type)
        return { type: doc.type, filePath }
      })

      const uploadResults = await Promise.all(uploadPromises)

      // Build results object
      uploadResults.forEach(result => {
        results[result.type] = result.filePath
      })

      return results as Record<KYCDocumentType, string>
    } catch (error) {
      console.error('Error uploading multiple KYC documents:', error)
      throw error
    }
  }

  /**
   * Get signed URL for KYC document (for admin review)
   */
  static async getKYCDocumentSignedUrl(filePath: string, expiresIn: number = 3600): Promise<string> {
    try {
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .createSignedUrl(filePath, expiresIn)

      if (error) {
        console.error('Error creating signed URL:', error)
        throw new Error(`Failed to create signed URL: ${error.message}`)
      }

      return data.signedUrl
    } catch (error) {
      console.error('Error getting KYC document signed URL:', error)
      throw error
    }
  }

  /**
   * Delete KYC document (for cleanup or resubmission)
   */
  static async deleteKYCDocument(filePath: string): Promise<void> {
    try {
      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([filePath])

      if (error) {
        console.error('Error deleting KYC document:', error)
        throw new Error(`Failed to delete document: ${error.message}`)
      }

      console.log('KYC document deleted successfully:', filePath)
    } catch (error) {
      console.error('Error deleting KYC document:', error)
      throw error
    }
  }

  /**
   * Get multiple signed URLs for KYC documents
   */
  static async getKYCDocumentSignedUrls(
    filePaths: string[], 
    expiresIn: number = 3600
  ): Promise<Record<string, string>> {
    try {
      const urlPromises = filePaths.map(async (filePath) => {
        const signedUrl = await this.getKYCDocumentSignedUrl(filePath, expiresIn)
        return { filePath, signedUrl }
      })

      const results = await Promise.all(urlPromises)
      
      const urlMap: Record<string, string> = {}
      results.forEach(result => {
        urlMap[result.filePath] = result.signedUrl
      })

      return urlMap
    } catch (error) {
      console.error('Error getting multiple KYC document signed URLs:', error)
      throw error
    }
  }

  /**
   * Validate document type enum
   */
  static isValidDocumentType(type: string): type is KYCDocumentType {
    return ['id_front', 'id_back', 'selfie', 'address_proof'].includes(type)
  }

  /**
   * Get human-readable document type name
   */
  static getDocumentTypeName(type: KYCDocumentType): string {
    const names: Record<KYCDocumentType, string> = {
      id_front: 'ID Document (Front)',
      id_back: 'ID Document (Back)',
      selfie: 'Selfie Photo',
      address_proof: 'Address Proof'
    }
    return names[type]
  }

  /**
   * Get document type requirements
   */
  static getDocumentTypeRequirements(type: KYCDocumentType): string {
    const requirements: Record<KYCDocumentType, string> = {
      id_front: 'Clear photo of the front side of your government-issued ID (National ID, Passport, or Driving License)',
      id_back: 'Clear photo of the back side of your government-issued ID',
      selfie: 'Clear selfie photo holding your ID document next to your face',
      address_proof: 'Recent utility bill, bank statement, or official document showing your address (PDF or image)'
    }
    return requirements[type]
  }
}
