'use client'

import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Create Supabase client with optimized settings for performance
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    // Reduce auto-refresh frequency to improve performance
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    // Disable automatic session recovery on visibility change
    // This prevents unnecessary re-fetches when switching tabs
    flowType: 'pkce'
  },
  realtime: {
    // Only enable realtime for specific channels when needed
    params: {
      eventsPerSecond: 10
    }
  },
  global: {
    headers: {
      'x-client-info': 'okdoi-marketplace'
    }
  }
})

// Table names for consistency
export const TABLES = {
  USERS: 'users',
  CATEGORIES: 'categories',
  SUBCATEGORIES: 'subcategories',
  ADS: 'ads',
  AD_IMAGES: 'ad_images',
  CHAT_CONVERSATIONS: 'chat_conversations',
  CHAT_MESSAGES: 'chat_messages',
  VENDOR_SHOPS: 'vendor_shops',
  SHOP_PRODUCTS: 'shop_products',
  SHOP_PRODUCT_IMAGES: 'shop_product_images',
  SHOP_CATEGORIES: 'shop_categories',
  SHOP_SUBCATEGORIES: 'shop_subcategories',
  SHOP_REVIEWS: 'shop_reviews',
  SHOP_FOLLOWERS: 'shop_followers',
  USER_FAVORITES: 'user_favorites',
  DISTRICTS: 'districts',
  CITIES: 'cities',
  PRODUCT_REVIEWS: 'product_reviews',
  USER_WALLETS: 'user_wallets',
  WALLET_TRANSACTIONS: 'wallet_transactions',
  P2P_TRANSFERS: 'p2p_transfers',
  DEPOSIT_REQUESTS: 'deposit_requests',
  WITHDRAWAL_REQUESTS: 'withdrawal_requests',
  SUBSCRIPTION_PACKAGES: 'subscription_packages',
  USER_SUBSCRIPTIONS: 'user_subscriptions',
  AD_BOOSTS: 'ad_boosts',
  BOOST_PACKAGES: 'boost_packages',
  // Order Management System
  CART_ITEMS: 'cart_items',
  SHOP_ORDERS: 'shop_orders',
  ORDER_ITEMS: 'order_items',
  ORDER_STATUS_HISTORY: 'order_status_history',
  // KYC System
  KYC_SUBMISSIONS: 'kyc_submissions',
  KYC_STATUS_HISTORY: 'kyc_status_history',
  KYC_DOCUMENT_TYPES: 'kyc_document_types'
} as const
