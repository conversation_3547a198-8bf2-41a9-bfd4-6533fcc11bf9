import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { KYCService } from '@/lib/services/kyc'

export interface KYCStatus {
  isVerified: boolean
  status: string
  submittedAt?: string
  approvedAt?: string
  loading: boolean
  error?: string
}

export function useKYCVerification() {
  const { user } = useAuth()
  const [kycStatus, setKycStatus] = useState<KYCStatus>({
    isVerified: false,
    status: 'not_submitted',
    loading: true
  })

  useEffect(() => {
    if (user) {
      fetchKYCStatus()
    } else {
      setKycStatus({
        isVerified: false,
        status: 'not_submitted',
        loading: false
      })
    }
  }, [user])

  const fetchKYCStatus = async () => {
    if (!user) return

    try {
      setKycStatus(prev => ({ ...prev, loading: true, error: undefined }))
      
      const status = await KYCService.getUserKYCStatus(user.id)
      
      setKycStatus({
        isVerified: status?.kyc_status === 'approved',
        status: status?.kyc_status || 'not_submitted',
        submittedAt: status?.kyc_submitted_at,
        approvedAt: status?.kyc_approved_at,
        loading: false
      })
    } catch (error) {
      console.error('Error fetching KYC status:', error)
      setKycStatus(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch KYC status'
      }))
    }
  }

  const checkKYCRequired = (action: string): { allowed: boolean; message?: string } => {
    if (kycStatus.loading) {
      return { allowed: false, message: 'Checking verification status...' }
    }

    if (!kycStatus.isVerified) {
      const actionMessages: Record<string, string> = {
        withdrawal: 'You must complete KYC verification before making withdrawals. Please go to Profile > Identity Verification to submit your documents.',
        p2p_transfer: 'You must complete KYC verification before making P2P transfers. Please go to Profile > Identity Verification to submit your documents.',
        shop_creation: 'You must complete KYC verification before creating a shop. Please go to Profile > Identity Verification to submit your documents.',
        default: 'You must complete KYC verification to perform this action. Please go to Profile > Identity Verification to submit your documents.'
      }

      return {
        allowed: false,
        message: actionMessages[action] || actionMessages.default
      }
    }

    return { allowed: true }
  }

  const refreshKYCStatus = () => {
    if (user) {
      fetchKYCStatus()
    }
  }

  return {
    kycStatus,
    checkKYCRequired,
    refreshKYCStatus
  }
}

// Higher-order component for KYC protection
export function withKYCVerification<T extends object>(
  Component: React.ComponentType<T>,
  requiredAction: string = 'default'
) {
  return function KYCProtectedComponent(props: T) {
    const { kycStatus, checkKYCRequired } = useKYCVerification()
    const verification = checkKYCRequired(requiredAction)

    if (kycStatus.loading) {
      return (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-blue"></div>
        </div>
      )
    }

    if (!verification.allowed) {
      return (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-yellow-100 rounded-full">
              <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">
            KYC Verification Required
          </h3>
          <p className="text-yellow-700 mb-4">
            {verification.message}
          </p>
          <a
            href="/dashboard/profile"
            className="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
          >
            Complete Verification
          </a>
        </div>
      )
    }

    return <Component {...props} />
  }
}

// Hook for checking specific KYC requirements
export function useKYCCheck() {
  const { kycStatus, checkKYCRequired } = useKYCVerification()

  const requireKYCForAction = async (action: string): Promise<boolean> => {
    const verification = checkKYCRequired(action)
    
    if (!verification.allowed && verification.message) {
      // You can integrate with your alert system here
      console.warn('KYC verification required:', verification.message)
      return false
    }

    return verification.allowed
  }

  return {
    kycStatus,
    requireKYCForAction,
    isKYCVerified: kycStatus.isVerified
  }
}
