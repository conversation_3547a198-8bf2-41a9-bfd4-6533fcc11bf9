'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  FileText,
  Eye,
  Heart,
  Store,
  TrendingUp,
  Calendar,
  Plus,
  ArrowRight,
  Gift,
  Users,
  Star,
  Sparkles,
  Target,
  Award,
  Zap
} from 'lucide-react'
import Link from 'next/link'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { PremiumButton, PremiumCard, FadeIn, SlideInUp, StaggerContainer, StaggerItem } from '@/components/ui/premium'
import { useAuth } from '@/contexts/AuthContext'
import { AdService } from '@/lib/services/ads'
import { VendorShopService } from '@/lib/services/vendorShops'
import { SubscriptionService } from '@/lib/services/subscriptions'
import { FavoritesService } from '@/lib/services/favorites'
import { usePageRefresh } from '@/hooks/usePageRefresh'
import { AdWithDetails, VendorShop } from '@/types'
import { SubscriptionUsage } from '@/lib/services/subscriptions'

interface DashboardStats {
  totalAds: number
  activeAds: number
  totalViews: number
  favoriteAds: number
  shops: VendorShop[]
  subscription?: SubscriptionUsage
}

interface StatCardProps {
  title: string
  value: string | number
  icon: React.ElementType
  color: string
  change?: number
  href?: string
}

function StatCard({ title, value, icon: Icon, color, change, href }: StatCardProps) {
  const content = (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-2">
            {typeof value === 'number' ? value.toLocaleString() : value}
          </p>
          {change !== undefined && (
            <p className={`text-sm mt-2 flex items-center ${
              change >= 0 ? 'text-green-600' : 'text-red-600'
            }`}>
              <TrendingUp className="h-4 w-4 mr-1" />
              {change >= 0 ? '+' : ''}{change}% this month
            </p>
          )}
        </div>
        <div className={`p-3 rounded-lg ${color}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
      </div>
    </div>
  )

  return href ? <Link href={href}>{content}</Link> : content
}

export default function Dashboard() {
  const { user } = useAuth()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentAds, setRecentAds] = useState<AdWithDetails[]>([])
  const [subscriptionUsage, setSubscriptionUsage] = useState<SubscriptionUsage | null>(null)
  const [freeAdsUsage, setFreeAdsUsage] = useState<{ freeAdsUsed: number; freeAdsLimit: number; canUseFreeAds: boolean } | null>(null)
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    if (user) {
      fetchDashboardData()
    }
  }, [user])

  // Use page refresh hook for smooth background updates
  usePageRefresh({
    onRefresh: () => fetchDashboardData(true),
    enabled: !!user
  })

  const fetchDashboardData = async (isRefresh = false) => {
    if (!user) return

    try {
      // Use refreshing state for background updates, loading for initial load
      if (isRefresh) {
        setRefreshing(true)
      } else {
        setLoading(true)
      }

      // Fetch user's ads
      const { ads } = await AdService.getAds({ userId: user.id }, 1, 5)
      setRecentAds(ads)

      // Calculate stats
      const totalViews = ads.reduce((sum, ad) => sum + (ad.views || 0), 0)
      const activeAds = ads.filter(ad => ad.status === 'active').length

      // Fetch user's shops
      const shops = await VendorShopService.getUserShops(user.id)

      // Fetch subscription usage
      const subscriptionData = await SubscriptionService.getUserSubscriptionUsage(user.id)
      setSubscriptionUsage(subscriptionData)

      // Fetch free ads usage if no active subscription
      if (!subscriptionData.has_active_subscription) {
        const freeAdsData = await SubscriptionService.getFreeAdsUsage(user.id)
        setFreeAdsUsage(freeAdsData)
      } else {
        setFreeAdsUsage(null)
      }

      // Fetch favorites count
      const favoritesCount = await FavoritesService.getUserFavoritesCount(user.id)

      setStats({
        totalAds: ads.length,
        activeAds,
        totalViews,
        favoriteAds: favoritesCount,
        shops,
        subscription: subscriptionData
      })
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="h-4 bg-gray-300 rounded w-3/4 mb-4"></div>
                <div className="h-8 bg-gray-300 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-300 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Enhanced Welcome Section */}
        <FadeIn>
          <PremiumCard variant="premium" className="overflow-hidden relative">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary-blue via-secondary-blue to-primary-600 opacity-90"></div>
            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-white/10"></div>

            {/* Floating Elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              {[...Array(5)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2 bg-white/20"
                  style={{
                    left: `${20 + i * 20}%`,
                    top: `${30 + (i % 2) * 30}%`
                  }}
                  animate={{
                    y: [0, -15, 0],
                    opacity: [0.2, 0.8, 0.2],
                    scale: [1, 1.2, 1]
                  }}
                  transition={{
                    duration: 2 + i * 0.5,
                    repeat: Infinity,
                    delay: i * 0.6
                  }}
                />
              ))}
            </div>

            <div className="relative z-10 p-8 text-white">
              {/* Refresh Indicator */}
              <AnimatePresence>
                {refreshing && (
                  <motion.div
                    className="absolute top-4 right-4"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                  >
                    <motion.div
                      className="w-6 h-6 border-2 border-white border-t-transparent"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                    />
                  </motion.div>
                )}
              </AnimatePresence>

              <motion.div
                className="flex items-center gap-4 mb-4"
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
              >
                <motion.div
                  className="p-3 bg-white/20 backdrop-blur-sm"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <Target className="h-8 w-8" />
                </motion.div>
                <div>
                  <motion.h1
                    className="text-3xl font-bold font-heading"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    Welcome back, {user?.full_name || 'User'}!
                  </motion.h1>
                  <motion.div
                    className="flex items-center gap-2 mt-1"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    <Award className="h-4 w-4 text-yellow-300" />
                    <span className="text-blue-100 font-medium">Dashboard Overview</span>
                  </motion.div>
                </div>
              </motion.div>

              <motion.p
                className="text-xl text-blue-100 mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                Manage your ads, track performance, and grow your business on OKDOI.
              </motion.p>

              <motion.div
                className="flex items-center gap-4"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                <PremiumButton
                  variant="secondary"
                  onClick={() => window.location.href = '/post-ad'}
                  icon={<Plus className="h-4 w-4" />}
                  className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white border-white/30"
                >
                  Post New Ad
                </PremiumButton>
                <div className="flex items-center gap-2 text-sm text-blue-100">
                  <Sparkles className="h-4 w-4 text-yellow-300" />
                  <span>Ready to boost your sales?</span>
                </div>
              </motion.div>
            </div>
          </PremiumCard>
        </FadeIn>

        {/* Enhanced Stats Grid */}
        <SlideInUp delay={0.2}>
          <StaggerContainer>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <StaggerItem>
                <motion.div
                  whileHover={{ scale: 1.02, y: -5 }}
                  transition={{ duration: 0.2 }}
                >
                  <StatCard
                    title="Total Ads"
                    value={stats?.totalAds || 0}
                    icon={FileText}
                    color="bg-blue-500"
                    change={12}
                    href="/dashboard/ads"
                  />
                </motion.div>
              </StaggerItem>

              <StaggerItem>
                <motion.div
                  whileHover={{ scale: 1.02, y: -5 }}
                  transition={{ duration: 0.2 }}
                >
                  <StatCard
                    title="Active Ads"
                    value={stats?.activeAds || 0}
                    icon={Eye}
                    color="bg-green-500"
                    change={8}
                    href="/dashboard/ads?status=active"
                  />
                </motion.div>
              </StaggerItem>

              <StaggerItem>
                <motion.div
                  whileHover={{ scale: 1.02, y: -5 }}
                  transition={{ duration: 0.2 }}
                >
                  <StatCard
                    title="Total Views"
                    value={stats?.totalViews || 0}
                    icon={TrendingUp}
                    color="bg-purple-500"
                    change={15}
                  />
                </motion.div>
              </StaggerItem>

              <StaggerItem>
                <motion.div
                  whileHover={{ scale: 1.02, y: -5 }}
                  transition={{ duration: 0.2 }}
                >
                  <StatCard
                    title="Favorites"
                    value={stats?.favoriteAds || 0}
                    icon={Heart}
                    color="bg-red-500"
                    href="/dashboard/favorites"
                  />
                </motion.div>
              </StaggerItem>
            </div>
          </StaggerContainer>
        </SlideInUp>

        {/* Subscription Stats */}
        {subscriptionUsage && subscriptionUsage.has_active_subscription && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Subscription Status</h3>
              <Link
                href="/dashboard/subscription"
                className="text-primary-blue hover:text-primary-blue/80 font-medium text-sm flex items-center"
              >
                Manage Subscription
                <ArrowRight className="h-4 w-4 ml-1" />
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-blue-600">Ads Remaining</p>
                    <p className="text-2xl font-bold text-blue-900 mt-1">
                      {subscriptionUsage.ads_remaining}
                    </p>
                  </div>
                  <FileText className="h-8 w-8 text-blue-500" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-green-600">Boosts Remaining</p>
                    <p className="text-2xl font-bold text-green-900 mt-1">
                      {subscriptionUsage.boosts_remaining}
                    </p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-green-500" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-purple-600">Ads Used</p>
                    <p className="text-2xl font-bold text-purple-900 mt-1">
                      {subscriptionUsage.ads_used}
                    </p>
                  </div>
                  <Eye className="h-8 w-8 text-purple-500" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-orange-600">Expires</p>
                    <p className="text-sm font-bold text-orange-900 mt-1">
                      {subscriptionUsage.subscription_expires_at
                        ? new Date(subscriptionUsage.subscription_expires_at).toLocaleDateString()
                        : 'N/A'
                      }
                    </p>
                  </div>
                  <Calendar className="h-8 w-8 text-orange-500" />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* No Subscription Message with Free Ads Info */}
        {subscriptionUsage && !subscriptionUsage.has_active_subscription && (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center mb-3">
                  <Gift className="h-6 w-6 text-blue-600 mr-2" />
                  <h3 className="text-lg font-semibold text-blue-900">
                    {freeAdsUsage && freeAdsUsage.canUseFreeAds ? 'Free Ads Available' : 'Free Ads Used Up'}
                  </h3>
                </div>

                {freeAdsUsage ? (
                  <div className="space-y-3">
                    <p className="text-blue-700">
                      {freeAdsUsage.canUseFreeAds ? (
                        <>
                          You have <span className="font-semibold">{freeAdsUsage.freeAdsLimit - freeAdsUsage.freeAdsUsed} free ads</span> remaining out of {freeAdsUsage.freeAdsLimit} available for new users.
                        </>
                      ) : (
                        <>
                          You've used all <span className="font-semibold">{freeAdsUsage.freeAdsLimit} free ads</span>. Subscribe to continue posting unlimited ads.
                        </>
                      )}
                    </p>

                    {/* Free Ads Progress Bar */}
                    <div className="bg-white rounded-lg p-3">
                      <div className="flex justify-between text-sm mb-2">
                        <span className="text-blue-600 font-medium">Free Ads Used:</span>
                        <span className="text-blue-900 font-semibold">{freeAdsUsage.freeAdsUsed} / {freeAdsUsage.freeAdsLimit}</span>
                      </div>
                      <div className="w-full bg-blue-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${(freeAdsUsage.freeAdsUsed / freeAdsUsage.freeAdsLimit) * 100}%` }}
                        ></div>
                      </div>
                    </div>

                    {freeAdsUsage.canUseFreeAds && (
                      <p className="text-sm text-blue-600">
                        💡 <strong>Tip:</strong> Subscribe to get unlimited ads, priority support, and boost features!
                      </p>
                    )}
                  </div>
                ) : (
                  <p className="text-blue-700">
                    Subscribe to a package to post ads and boost your listings for better visibility.
                  </p>
                )}
              </div>

              <div className="ml-6 flex flex-col space-y-2">
                {freeAdsUsage && freeAdsUsage.canUseFreeAds && (
                  <Link
                    href="/post-ad"
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center text-sm"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Post Free Ad
                  </Link>
                )}
                <Link
                  href="/dashboard/subscription"
                  className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center text-sm"
                >
                  <Star className="h-4 w-4 mr-1" />
                  Subscribe Now
                </Link>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Recent Ads */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900">Recent Ads</h3>
                <Link
                  href="/dashboard/ads"
                  className="text-primary-blue hover:text-primary-blue/80 font-medium text-sm flex items-center"
                >
                  View All
                  <ArrowRight className="h-4 w-4 ml-1" />
                </Link>
              </div>
              
              {recentAds.length > 0 ? (
                <div className="space-y-4">
                  {recentAds.map((ad) => (
                    <div key={ad.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="w-16 h-16 bg-gray-200 rounded-lg flex-shrink-0 overflow-hidden">
                        {ad.ad_images && ad.ad_images.length > 0 ? (
                          <img
                            src={ad.ad_images[0].image_url}
                            alt={ad.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <FileText className="h-6 w-6 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {ad.title}
                        </h4>
                        <p className="text-sm text-gray-500">
                          Rs {ad.price?.toLocaleString()} • {ad.views || 0} views
                        </p>
                        <p className="text-xs text-gray-400">
                          {new Date(ad.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex-shrink-0">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          ad.status === 'active' ? 'bg-green-100 text-green-800' :
                          ad.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {ad.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">No ads yet</h4>
                  <p className="text-gray-600 mb-4">Start by posting your first ad</p>
                  <Link
                    href="/post-ad"
                    className="inline-flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Post Your First Ad
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions & Info */}
          <div className="space-y-6">
            {/* Enhanced Quick Actions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <PremiumCard variant="premium">
                <h3 className="text-lg font-bold font-heading text-gray-900 mb-4 flex items-center">
                  <Zap className="h-5 w-5 text-primary-blue mr-2" />
                  Quick Actions
                </h3>
                <div className="space-y-3">
                  <motion.div
                    whileHover={{ scale: 1.02, x: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Link
                      href="/post-ad"
                      className="flex items-center p-4 bg-gradient-to-r from-primary-blue/10 to-primary-blue/5 rounded-lg hover:from-primary-blue/20 hover:to-primary-blue/10 transition-all duration-300 border border-primary-blue/20 hover:border-primary-blue/30"
                    >
                      <motion.div
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Plus className="h-5 w-5 text-primary-blue mr-3" />
                      </motion.div>
                      <span className="font-bold text-primary-blue">Post New Ad</span>
                      <ArrowRight className="h-4 w-4 text-primary-blue ml-auto" />
                    </Link>
                  </motion.div>

                  <motion.div
                    whileHover={{ scale: 1.02, x: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Link
                      href="/create-shop"
                      className="flex items-center p-4 bg-gradient-to-r from-green-50 to-green-25 rounded-lg hover:from-green-100 hover:to-green-50 transition-all duration-300 border border-green-200 hover:border-green-300"
                    >
                      <motion.div
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Store className="h-5 w-5 text-green-600 mr-3" />
                      </motion.div>
                      <span className="font-bold text-green-600">Create Shop</span>
                      <ArrowRight className="h-4 w-4 text-green-600 ml-auto" />
                    </Link>
                  </motion.div>

                  <motion.div
                    whileHover={{ scale: 1.02, x: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Link
                      href="/dashboard/referrals"
                      className="flex items-center p-4 bg-gradient-to-r from-purple-50 to-purple-25 rounded-lg hover:from-purple-100 hover:to-purple-50 transition-all duration-300 border border-purple-200 hover:border-purple-300"
                    >
                      <motion.div
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Gift className="h-5 w-5 text-purple-600 mr-3" />
                      </motion.div>
                      <span className="font-bold text-purple-600">Referral Program</span>
                      <ArrowRight className="h-4 w-4 text-purple-600 ml-auto" />
                    </Link>
                  </motion.div>
                </div>
              </PremiumCard>
            </motion.div>

            {/* My Shops */}
            {stats?.shops && stats.shops.length > 0 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">My Shops</h3>
                  <Link
                    href="/dashboard/shop"
                    className="text-primary-blue hover:text-primary-blue/80 font-medium text-sm"
                  >
                    View All
                  </Link>
                </div>
                <div className="space-y-3">
                  {stats.shops.slice(0, 2).map((shop) => (
                    <div key={shop.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                      <div className="w-10 h-10 bg-primary-blue/10 rounded-lg flex items-center justify-center">
                        <Store className="h-5 w-5 text-primary-blue" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {shop.name}
                        </p>
                        <div className="flex items-center text-xs text-gray-500">
                          <Star className="h-3 w-3 mr-1" />
                          {shop.rating.toFixed(1)} • {shop.total_products} products
                        </div>
                      </div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        shop.status === 'approved' ? 'bg-green-100 text-green-800' :
                        shop.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {shop.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Account Info */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Account Info</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Member since</span>
                  <span className="font-medium text-gray-900">
                    {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total referrals</span>
                  <span className="font-medium text-gray-900">
                    {user?.total_referrals || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Rewards earned</span>
                  <span className="font-medium text-gray-900">
                    Rs {user?.total_rewards?.toLocaleString() || 0}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
