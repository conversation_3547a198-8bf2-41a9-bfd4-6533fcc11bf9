import { createBrowserClient } from '@supabase/ssr'
import { createClient } from '@supabase/supabase-js'

// Environment variables with validation
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Validate required environment variables
if (!supabaseUrl) {
  console.error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
}

if (!supabaseAnonKey) {
  console.error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')
}

if (!supabaseServiceRoleKey) {
  console.warn('Missing SUPABASE_SERVICE_ROLE_KEY environment variable - admin functions will not work')
}

// Create browser client with error handling
let supabase: ReturnType<typeof createBrowserClient>
try {
  supabase = createBrowserClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      get(name: string) {
        if (typeof document !== 'undefined') {
          const value = document.cookie
            .split('; ')
            .find(row => row.startsWith(`${name}=`))
            ?.split('=')[1]
          return value ? decodeURIComponent(value) : undefined
        }
        return undefined
      },
      set(name: string, value: string, options: any) {
        if (typeof document !== 'undefined') {
          let cookieString = `${name}=${encodeURIComponent(value)}`
          if (options?.maxAge) cookieString += `; max-age=${options.maxAge}`
          if (options?.path) cookieString += `; path=${options.path}`
          if (options?.domain) cookieString += `; domain=${options.domain}`
          if (options?.secure) cookieString += '; secure'
          if (options?.httpOnly) cookieString += '; httponly'
          if (options?.sameSite) cookieString += `; samesite=${options.sameSite}`
          document.cookie = cookieString
        }
      },
      remove(name: string, options: any) {
        if (typeof document !== 'undefined') {
          let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`
          if (options?.path) cookieString += `; path=${options.path}`
          if (options?.domain) cookieString += `; domain=${options.domain}`
          document.cookie = cookieString
        }
      }
    }
  })
} catch (error) {
  console.error('Failed to create Supabase browser client:', error)
  // Fallback to basic client without SSR
  supabase = createClient(supabaseUrl, supabaseAnonKey) as any
}

// Admin client with service role key for bypassing RLS
export const supabaseAdmin = supabaseServiceRoleKey
  ? createClient(supabaseUrl, supabaseServiceRoleKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
  : null

export { supabase }

// Database table names
export const TABLES = {
  CATEGORIES: 'categories',
  SUBCATEGORIES: 'subcategories',
  ADS: 'ads',
  USERS: 'users',
  AD_IMAGES: 'ad_images',
  DISTRICTS: 'districts',
  CITIES: 'cities',
  USER_FAVORITES: 'user_favorites',
  VENDOR_SHOPS: 'vendor_shops',
  SHOP_PRODUCTS: 'shop_products',
  SHOP_PRODUCT_IMAGES: 'shop_product_images',
  SHOP_REVIEWS: 'shop_reviews',
  SHOP_FOLLOWERS: 'shop_followers',
  SHOP_CATEGORIES: 'shop_categories',
  SHOP_SUBCATEGORIES: 'shop_subcategories',
  PRODUCT_REVIEWS: 'product_reviews',
  CHAT_CONVERSATIONS: 'chat_conversations',
  CHAT_MESSAGES: 'chat_messages',
  USER_WALLETS: 'user_wallets',
  WALLET_TRANSACTIONS: 'wallet_transactions',
  P2P_TRANSFERS: 'p2p_transfers',
  DEPOSIT_REQUESTS: 'deposit_requests',
  WITHDRAWAL_REQUESTS: 'withdrawal_requests',
  SUBSCRIPTION_PACKAGES: 'subscription_packages',
  USER_SUBSCRIPTIONS: 'user_subscriptions',
  AD_BOOSTS: 'ad_boosts',
  BOOST_PACKAGES: 'boost_packages',
  // Order Management System
  CART_ITEMS: 'cart_items',
  SHOP_ORDERS: 'shop_orders',
  ORDER_ITEMS: 'order_items',
  ORDER_STATUS_HISTORY: 'order_status_history',
  // Merchant Wallet System
  MERCHANT_WALLETS: 'merchant_wallets',
  MERCHANT_WALLET_TRANSACTIONS: 'merchant_wallet_transactions',
  MERCHANT_TO_MAIN_TRANSFERS: 'merchant_to_main_transfers',
  // Referral & Commission System
  REFERRAL_HIERARCHY: 'referral_hierarchy',
  COMMISSION_STRUCTURE: 'commission_structure',
  COMMISSION_TRANSACTIONS: 'commission_transactions',
  REFERRAL_PLACEMENTS: 'referral_placements',
  // KYC System
  KYC_SUBMISSIONS: 'kyc_submissions',
  KYC_STATUS_HISTORY: 'kyc_status_history',
  KYC_DOCUMENT_TYPES: 'kyc_document_types',
  ZONAL_MANAGERS: 'zonal_managers',
  REGIONAL_SALES_MANAGERS: 'regional_sales_managers'
} as const
