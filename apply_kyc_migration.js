const { createClient } = require('@supabase/supabase-js')
require('dotenv').config()

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing required environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function applyKYCMigration() {
  console.log('Starting KYC migration...')
  
  try {
    // Step 1: Add KYC columns to users table
    console.log('Step 1: Adding KYC columns to users table...')
    const { error: usersError } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_status varchar(20) DEFAULT 'not_submitted' 
        CHECK (kyc_status IN ('not_submitted', 'pending', 'approved', 'rejected'));
        
        ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_submitted_at timestamp with time zone;
        ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_approved_at timestamp with time zone;
      `
    })
    
    if (usersError) {
      console.error('Error adding KYC columns to users table:', usersError)
      return
    }
    console.log('✓ KYC columns added to users table')
    
    // Step 2: Create KYC submissions table
    console.log('Step 2: Creating KYC submissions table...')
    const { error: submissionsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS kyc_submissions (
          id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
          
          -- Document URLs (stored in Supabase Storage)
          id_document_front_url text NOT NULL,
          id_document_back_url text NOT NULL,
          selfie_photo_url text NOT NULL,
          address_proof_url text NOT NULL,
          
          -- Document metadata
          id_document_type varchar(50) NOT NULL CHECK (id_document_type IN ('national_id', 'passport', 'driving_license')),
          id_document_number varchar(100),
          
          -- Personal information for verification
          full_name varchar(255) NOT NULL,
          date_of_birth date,
          address text NOT NULL,
          
          -- Submission status and tracking
          status varchar(20) DEFAULT 'pending' CHECK (status IN ('pending', 'under_review', 'approved', 'rejected')),
          submission_notes text,
          
          -- Admin review fields
          reviewed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
          reviewed_at timestamp with time zone,
          rejection_reason text,
          admin_notes text,
          
          -- Timestamps
          created_at timestamp with time zone DEFAULT now(),
          updated_at timestamp with time zone DEFAULT now(),
          
          -- Ensure one active submission per user
          UNIQUE(user_id)
        );
      `
    })
    
    if (submissionsError) {
      console.error('Error creating KYC submissions table:', submissionsError)
      return
    }
    console.log('✓ KYC submissions table created')
    
    // Step 3: Create KYC document types table
    console.log('Step 3: Creating KYC document types table...')
    const { error: docTypesError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS kyc_document_types (
          id varchar(50) PRIMARY KEY,
          name varchar(100) NOT NULL,
          description text,
          is_active boolean DEFAULT true,
          created_at timestamp with time zone DEFAULT now()
        );
        
        INSERT INTO kyc_document_types (id, name, description) VALUES
        ('national_id', 'National Identity Card', 'Sri Lankan National Identity Card'),
        ('passport', 'Passport', 'Valid passport document'),
        ('driving_license', 'Driving License', 'Valid driving license')
        ON CONFLICT (id) DO NOTHING;
      `
    })
    
    if (docTypesError) {
      console.error('Error creating KYC document types table:', docTypesError)
      return
    }
    console.log('✓ KYC document types table created')
    
    console.log('KYC migration completed successfully!')
    
  } catch (error) {
    console.error('Migration failed:', error)
  }
}

applyKYCMigration()
