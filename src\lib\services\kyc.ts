import { supabase } from '@/lib/supabase'
import { TABLES } from '@/lib/supabaseConfig'
import { KYCStorageService, KYCDocumentType, KYCDocumentUpload } from './kycStorage'

export interface KYCSubmission {
  id: string
  user_id: string
  id_document_front_url: string
  id_document_back_url: string
  selfie_photo_url: string
  address_proof_url: string
  id_document_type: 'national_id' | 'passport' | 'driving_license'
  id_document_number?: string
  full_name: string
  date_of_birth?: string
  address: string
  status: 'pending' | 'under_review' | 'approved' | 'rejected'
  submission_notes?: string
  reviewed_by?: string
  reviewed_at?: string
  rejection_reason?: string
  admin_notes?: string
  created_at: string
  updated_at: string
}

export interface KYCStatusHistory {
  id: string
  kyc_submission_id: string
  user_id: string
  previous_status?: string
  new_status: string
  changed_by?: string
  change_reason?: string
  admin_notes?: string
  created_at: string
}

export interface KYCSubmissionData {
  id_document_type: 'national_id' | 'passport' | 'driving_license'
  id_document_number?: string
  full_name: string
  date_of_birth?: string
  address: string
  submission_notes?: string
}

export interface KYCDocuments {
  id_front: File
  id_back: File
  selfie: File
  address_proof: File
}

export interface KYCReviewData {
  status: 'approved' | 'rejected'
  rejection_reason?: string
  admin_notes?: string
}

export class KYCService {
  /**
   * Get user's KYC status from users table
   */
  static async getUserKYCStatus(userId: string): Promise<{
    kyc_status: string
    kyc_submitted_at?: string
    kyc_approved_at?: string
  } | null> {
    try {
      const { data, error } = await supabase
        .from(TABLES.USERS)
        .select('kyc_status, kyc_submitted_at, kyc_approved_at')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching user KYC status:', error)
        // Return default status if columns don't exist yet
        if (error.message.includes('column') && error.message.includes('does not exist')) {
          return {
            kyc_status: 'not_submitted'
          }
        }
        throw new Error(`Failed to fetch KYC status: ${error.message}`)
      }

      return data || { kyc_status: 'not_submitted' }
    } catch (error) {
      console.error('Error getting user KYC status:', error)
      // Return default status for any database errors
      return { kyc_status: 'not_submitted' }
    }
  }

  /**
   * Get user's KYC submission details
   */
  static async getUserKYCSubmission(userId: string): Promise<KYCSubmission | null> {
    try {
      const { data, error } = await supabase
        .from(TABLES.KYC_SUBMISSIONS)
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          // No submission found
          return null
        }
        // Handle table not found error
        if (error.message.includes('Could not find the table') ||
            error.message.includes('schema cache')) {
          console.warn('KYC submissions table not found - database migration may be needed')
          return null
        }
        console.error('Error fetching KYC submission:', error)
        throw new Error(`Failed to fetch KYC submission: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error('Error getting user KYC submission:', error)
      // Return null for table not found errors instead of throwing
      if (error instanceof Error &&
          (error.message.includes('Could not find the table') ||
           error.message.includes('schema cache'))) {
        return null
      }
      throw error
    }
  }

  /**
   * Submit KYC application with documents
   */
  static async submitKYCApplication(
    userId: string,
    submissionData: KYCSubmissionData,
    documents: KYCDocuments
  ): Promise<KYCSubmission> {
    try {
      // Check if user already has a submission
      const existingSubmission = await this.getUserKYCSubmission(userId)
      if (existingSubmission && existingSubmission.status !== 'rejected') {
        throw new Error('You already have a pending or approved KYC submission')
      }

      // Prepare document uploads
      const documentUploads: KYCDocumentUpload[] = [
        { file: documents.id_front, type: 'id_front' },
        { file: documents.id_back, type: 'id_back' },
        { file: documents.selfie, type: 'selfie' },
        { file: documents.address_proof, type: 'address_proof' }
      ]

      // Upload all documents
      console.log('Uploading KYC documents...')
      const uploadedDocuments = await KYCStorageService.uploadKYCDocuments(documentUploads, userId)

      // If there's an existing rejected submission, delete it first
      if (existingSubmission) {
        await this.deleteKYCSubmission(existingSubmission.id)
      }

      // Create KYC submission record
      const submissionRecord = {
        user_id: userId,
        id_document_front_url: uploadedDocuments.id_front,
        id_document_back_url: uploadedDocuments.id_back,
        selfie_photo_url: uploadedDocuments.selfie,
        address_proof_url: uploadedDocuments.address_proof,
        id_document_type: submissionData.id_document_type,
        id_document_number: submissionData.id_document_number,
        full_name: submissionData.full_name,
        date_of_birth: submissionData.date_of_birth,
        address: submissionData.address,
        submission_notes: submissionData.submission_notes,
        status: 'pending' as const
      }

      const { data, error } = await supabase
        .from(TABLES.KYC_SUBMISSIONS)
        .insert(submissionRecord)
        .select()
        .single()

      if (error) {
        console.error('Error creating KYC submission:', error)
        throw new Error(`Failed to submit KYC application: ${error.message}`)
      }

      console.log('KYC application submitted successfully')
      return data
    } catch (error) {
      console.error('Error submitting KYC application:', error)
      throw error
    }
  }

  /**
   * Get all KYC submissions for admin review
   */
  static async getAllKYCSubmissions(
    page: number = 1,
    limit: number = 20,
    status?: string,
    searchTerm?: string
  ): Promise<{
    submissions: (KYCSubmission & { user: { full_name: string; email: string } })[]
    total: number
  }> {
    try {
      let query = supabase
        .from(TABLES.KYC_SUBMISSIONS)
        .select(`
          *,
          user:users!kyc_submissions_user_id_fkey(full_name, email)
        `, { count: 'exact' })

      // Apply filters
      if (status) {
        query = query.eq('status', status)
      }

      if (searchTerm) {
        query = query.or(`full_name.ilike.%${searchTerm}%,id_document_number.ilike.%${searchTerm}%`)
      }

      // Apply pagination
      const offset = (page - 1) * limit
      query = query
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      const { data, error, count } = await query

      if (error) {
        console.error('Error fetching KYC submissions:', error)
        throw new Error(`Failed to fetch KYC submissions: ${error.message}`)
      }

      return {
        submissions: data || [],
        total: count || 0
      }
    } catch (error) {
      console.error('Error getting all KYC submissions:', error)
      throw error
    }
  }

  /**
   * Review KYC submission (admin only)
   */
  static async reviewKYCSubmission(
    submissionId: string,
    reviewData: KYCReviewData,
    reviewerId: string
  ): Promise<KYCSubmission> {
    try {
      const updateData = {
        status: reviewData.status,
        reviewed_by: reviewerId,
        reviewed_at: new Date().toISOString(),
        rejection_reason: reviewData.rejection_reason,
        admin_notes: reviewData.admin_notes
      }

      const { data, error } = await supabase
        .from(TABLES.KYC_SUBMISSIONS)
        .update(updateData)
        .eq('id', submissionId)
        .select()
        .single()

      if (error) {
        console.error('Error reviewing KYC submission:', error)
        throw new Error(`Failed to review KYC submission: ${error.message}`)
      }

      console.log(`KYC submission ${submissionId} reviewed as ${reviewData.status}`)
      return data
    } catch (error) {
      console.error('Error reviewing KYC submission:', error)
      throw error
    }
  }

  /**
   * Get KYC status history for a user
   */
  static async getKYCStatusHistory(userId: string): Promise<KYCStatusHistory[]> {
    try {
      const { data, error } = await supabase
        .from(TABLES.KYC_STATUS_HISTORY)
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching KYC status history:', error)
        throw new Error(`Failed to fetch KYC status history: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('Error getting KYC status history:', error)
      throw error
    }
  }

  /**
   * Check if user is KYC verified
   */
  static async isUserKYCVerified(userId: string): Promise<boolean> {
    try {
      const status = await this.getUserKYCStatus(userId)
      return status?.kyc_status === 'approved'
    } catch (error) {
      console.error('Error checking KYC verification status:', error)
      return false
    }
  }

  /**
   * Get KYC document signed URLs for admin review
   */
  static async getKYCDocumentUrls(submission: KYCSubmission): Promise<{
    id_front_url: string
    id_back_url: string
    selfie_url: string
    address_proof_url: string
  }> {
    try {
      const filePaths = [
        submission.id_document_front_url,
        submission.id_document_back_url,
        submission.selfie_photo_url,
        submission.address_proof_url
      ]

      const signedUrls = await KYCStorageService.getKYCDocumentSignedUrls(filePaths, 3600) // 1 hour expiry

      return {
        id_front_url: signedUrls[submission.id_document_front_url],
        id_back_url: signedUrls[submission.id_document_back_url],
        selfie_url: signedUrls[submission.selfie_photo_url],
        address_proof_url: signedUrls[submission.address_proof_url]
      }
    } catch (error) {
      console.error('Error getting KYC document URLs:', error)
      throw error
    }
  }

  /**
   * Delete KYC submission (for resubmission)
   */
  private static async deleteKYCSubmission(submissionId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from(TABLES.KYC_SUBMISSIONS)
        .delete()
        .eq('id', submissionId)

      if (error) {
        console.error('Error deleting KYC submission:', error)
        throw new Error(`Failed to delete KYC submission: ${error.message}`)
      }
    } catch (error) {
      console.error('Error deleting KYC submission:', error)
      throw error
    }
  }

  /**
   * Get KYC document types
   */
  static async getKYCDocumentTypes(): Promise<Array<{
    id: string
    name: string
    description: string
  }>> {
    try {
      const { data, error } = await supabase
        .from(TABLES.KYC_DOCUMENT_TYPES)
        .select('*')
        .eq('is_active', true)
        .order('name')

      if (error) {
        console.error('Error fetching KYC document types:', error)
        throw new Error(`Failed to fetch document types: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('Error getting KYC document types:', error)
      throw error
    }
  }
}
