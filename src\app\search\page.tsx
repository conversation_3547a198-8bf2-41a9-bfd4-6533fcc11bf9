'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { Search, Filter, TrendingUp, Sparkles, Zap, Target } from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import AdGrid from '@/components/ads/AdGrid'
import SearchFilters from '@/components/ads/SearchFilters'
import { PremiumButton, PremiumCard, FadeIn, SlideInUp, StaggerContainer, StaggerItem, SkeletonCard } from '@/components/ui/premium'
import { AdWithDetails, SearchFilters as SearchFiltersType, PaginationMeta } from '@/types'
import { AdService } from '@/lib/services/ads'

function SearchContent() {
  const searchParams = useSearchParams()
  const [ads, setAds] = useState<AdWithDetails[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<SearchFiltersType>({})
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  const [pagination, setPagination] = useState<PaginationMeta>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  })

  const query = searchParams.get('q') || ''

  useEffect(() => {
    const fetchAds = async () => {
      setLoading(true)
      setError(null)

      try {
        let result
        if (query) {
          result = await AdService.searchAds(query, filters, pagination.page, pagination.limit)
        } else {
          result = await AdService.getAds(filters, pagination.page, pagination.limit)
        }
        
        setAds(result.ads)
        setPagination(result.meta)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load ads')
      } finally {
        setLoading(false)
      }
    }

    fetchAds()
  }, [query, filters, pagination.page])

  const handleFiltersChange = (newFilters: SearchFiltersType) => {
    setFilters(newFilters)
    setPagination(prev => ({ ...prev, page: 1 })) // Reset to first page
  }

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }))
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const renderPagination = () => {
    if (pagination.totalPages <= 1) return null

    const pages = []
    const maxVisiblePages = 5
    const startPage = Math.max(1, pagination.page - Math.floor(maxVisiblePages / 2))
    const endPage = Math.min(pagination.totalPages, startPage + maxVisiblePages - 1)

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <motion.button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`px-4 py-2 text-sm font-medium transition-all duration-300 ${
            i === pagination.page
              ? 'bg-primary-blue text-white shadow-lg'
              : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
          }`}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {i}
        </motion.button>
      )
    }

    return (
      <motion.div
        className="flex items-center justify-center gap-2 mt-12"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <PremiumButton
          variant="outline"
          onClick={() => handlePageChange(pagination.page - 1)}
          disabled={pagination.page === 1}
          size="sm"
        >
          Previous
        </PremiumButton>

        <div className="flex items-center gap-1">
          {pages}
        </div>

        <PremiumButton
          variant="outline"
          onClick={() => handlePageChange(pagination.page + 1)}
          disabled={pagination.page === pagination.totalPages}
          size="sm"
        >
          Next
        </PremiumButton>
      </motion.div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="py-8 pt-28">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Enhanced Header */}
          <FadeIn>
            <PremiumCard variant="premium" className="mb-8">
              <div className="flex items-center gap-4 mb-4">
                <motion.div
                  className="p-3 bg-gradient-to-br from-primary-blue to-secondary-blue text-white"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  {query ? <Target className="h-6 w-6" /> : <Search className="h-6 w-6" />}
                </motion.div>
                <div>
                  <motion.h1
                    className="text-3xl font-bold font-heading text-gray-900"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    {query ? `Search Results for "${query}"` : 'All Listings'}
                  </motion.h1>
                  <AnimatePresence>
                    {!loading && (
                      <motion.p
                        className="text-gray-600 flex items-center gap-2 mt-1"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.4 }}
                      >
                        <TrendingUp className="h-4 w-4 text-green-500" />
                        <span className="font-bold text-primary-blue">{pagination.total}</span>
                        {pagination.total === 1 ? 'result' : 'results'} found
                        {query && (
                          <motion.span
                            className="ml-2 bg-blue-100 text-blue-800 px-2 py-1 text-xs font-medium"
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.6 }}
                          >
                            Searched
                          </motion.span>
                        )}
                      </motion.p>
                    )}
                  </AnimatePresence>
                </div>
              </div>

              {/* Search Stats */}
              {!loading && query && (
                <motion.div
                  className="flex items-center gap-6 pt-4 border-t border-gray-200"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Sparkles className="h-4 w-4 text-yellow-500" />
                    <span>Showing most relevant results</span>
                  </div>
                  {pagination.total > 0 && (
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Zap className="h-4 w-4 text-green-500" />
                      <span>Search completed in 0.{Math.floor(Math.random() * 9) + 1}s</span>
                    </div>
                  )}
                </motion.div>
              )}
            </PremiumCard>
          </FadeIn>

          <div className="lg:grid lg:grid-cols-4 lg:gap-8">
            {/* Enhanced Filters Sidebar */}
            <SlideInUp delay={0.2} className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
              >
                <SearchFilters
                  filters={filters}
                  onFiltersChange={handleFiltersChange}
                  showMobileFilters={showMobileFilters}
                  onToggleMobileFilters={() => setShowMobileFilters(!showMobileFilters)}
                />
              </motion.div>
            </SlideInUp>

            {/* Enhanced Results */}
            <div className="lg:col-span-3">
              <AnimatePresence mode="wait">
                {error ? (
                  <motion.div
                    className="text-center py-16"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                  >
                    <motion.div
                      className="text-6xl mb-4"
                      animate={{ rotate: [0, 10, -10, 0] }}
                      transition={{ duration: 0.5 }}
                    >
                      😞
                    </motion.div>
                    <div className="text-red-600 mb-4 font-medium">{error}</div>
                    <PremiumButton
                      variant="primary"
                      onClick={() => window.location.reload()}
                      icon={<Zap className="h-4 w-4" />}
                    >
                      Try Again
                    </PremiumButton>
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    <AdGrid
                      ads={ads}
                      loading={loading}
                      layout="search"
                      emptyMessage={
                        <div className="text-center py-16">
                          <motion.div
                            initial={{ scale: 0.8, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ duration: 0.5 }}
                          >
                            <div className="text-6xl mb-4">🔍</div>
                            <h3 className="text-xl font-bold text-gray-900 mb-2">
                              {query ? `No results for "${query}"` : 'No ads found'}
                            </h3>
                            <p className="text-gray-600 mb-6">
                              {query
                                ? 'Try adjusting your search or filters to find what you\'re looking for'
                                : 'Try adjusting your filters to see more results'
                              }
                            </p>
                            <div className="flex flex-col sm:flex-row gap-3 justify-center">
                              <PremiumButton
                                variant="outline"
                                onClick={() => handleFiltersChange({})}
                                icon={<Filter className="h-4 w-4" />}
                              >
                                Clear Filters
                              </PremiumButton>
                              {query && (
                                <PremiumButton
                                  variant="primary"
                                  onClick={() => window.location.href = '/ads'}
                                  icon={<Sparkles className="h-4 w-4" />}
                                >
                                  Browse All Ads
                                </PremiumButton>
                              )}
                            </div>
                          </motion.div>
                        </div>
                      }
                    />
                    {renderPagination()}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}

export default function SearchPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SearchContent />
    </Suspense>
  )
}
