/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/styles/hero-backgrounds.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/* Hero Background Images - WebP */

.hero-bg-1 {
  background-image: url('/images/hero/hero-bg-1.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-bg-2 {
  background-image: url('/images/hero/hero-bg-2.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-bg-3 {
  background-image: url('/images/hero/hero-bg-1.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  filter: hue-rotate(120deg) brightness(0.9);
}

.hero-bg-4 {
  background-image: url('/images/hero/hero-bg-2.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  filter: hue-rotate(240deg) brightness(1.1);
}

/* Add subtle patterns for visual interest */
.hero-bg-1::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 20% 30%, rgba(255,255,255,0.1) 2px, transparent 2px);
  background-size: 40px 40px;
  pointer-events: none;
}

.hero-bg-2::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: repeating-linear-gradient(45deg, transparent, transparent 35px, rgba(255,255,255,0.05) 35px, rgba(255,255,255,0.05) 70px);
  pointer-events: none;
}

.hero-bg-3::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: linear-gradient(90deg, transparent 49%, rgba(255,255,255,0.08) 50%, transparent 51%),
                    linear-gradient(0deg, transparent 49%, rgba(255,255,255,0.08) 50%, transparent 51%);
  background-size: 60px 60px;
  pointer-events: none;
}

.hero-bg-4::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 30px 30px;
  pointer-events: none;
}

