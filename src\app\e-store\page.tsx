'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Store,
  Search,
  Filter,
  Star,
  MapPin,
  Package,
  Users,
  Grid,
  List,
  SlidersHorizontal,
  Heart,
  ShoppingCart,
  Eye,
  Sparkles,
  TrendingUp,
  Award,
  Zap
} from 'lucide-react'
import Link from 'next/link'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import ProductCard from '@/components/products/ProductCard'
import FloatingCartButton from '@/components/cart/FloatingCartButton'
import { PremiumButton, PremiumCard, PremiumInput, FadeIn, SlideInUp, StaggerContainer, StaggerItem, SkeletonCard } from '@/components/ui/premium'
import { VendorShopService } from '@/lib/services/vendorShops'
import { ShopProductService } from '@/lib/services/shopProducts'
import { ShopCategoryService } from '@/lib/services/shopCategories'
import { VendorShop, ShopProduct, ShopCategory } from '@/types'

interface ProductFilters {
  search: string
  category_id: string
  subcategory_id: string
  shop_id: string
  featured: boolean
  min_price: string
  max_price: string
}

export default function EStorePage() {
  const [products, setProducts] = useState<ShopProduct[]>([])
  const [featuredShops, setFeaturedShops] = useState<VendorShop[]>([])
  const [categories, setCategories] = useState<ShopCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [showFilters, setShowFilters] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalProducts, setTotalProducts] = useState(0)
  const productsPerPage = 24

  const [filters, setFilters] = useState<ProductFilters>({
    search: '',
    category_id: '',
    subcategory_id: '',
    shop_id: '',
    featured: false,
    min_price: '',
    max_price: ''
  })

  useEffect(() => {
    loadInitialData()
  }, [])

  useEffect(() => {
    fetchProducts()
  }, [currentPage, filters])

  const loadInitialData = async () => {
    try {
      setLoading(true)

      // Load categories and featured shops in parallel
      const [categoriesData, featuredShopsData] = await Promise.all([
        ShopCategoryService.getAllCategories(),
        VendorShopService.getShops({ status: 'approved', featured: true }, 1, 8)
      ])

      setCategories(categoriesData)
      setFeaturedShops(featuredShopsData.shops)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data')
    }
  }

  const fetchProducts = async () => {
    try {
      setLoading(true)

      const productFilters: any = {}
      if (filters.category_id) productFilters.category_id = filters.category_id
      if (filters.subcategory_id) productFilters.subcategory_id = filters.subcategory_id
      if (filters.shop_id) productFilters.shop_id = filters.shop_id
      if (filters.search) productFilters.search = filters.search
      if (filters.featured) productFilters.featured = filters.featured

      const { products: productsData, total } = await ShopProductService.getAllProducts(
        currentPage,
        productsPerPage,
        productFilters
      )

      setProducts(productsData)
      setTotalProducts(total)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load products')
    } finally {
      setLoading(false)
    }
  }

  const handleFilterChange = (key: keyof ProductFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1) // Reset to first page when filters change
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      category_id: '',
      subcategory_id: '',
      shop_id: '',
      featured: false,
      min_price: '',
      max_price: ''
    })
    setCurrentPage(1)
  }

  const totalPages = Math.ceil(totalProducts / productsPerPage)

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pt-24">
        {/* Enhanced Hero Section */}
        <FadeIn>
          <PremiumCard variant="premium" className="mb-8 overflow-hidden relative">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary-blue via-secondary-blue to-primary-600 opacity-90"></div>
            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-white/10"></div>

            {/* Floating Elements */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2 bg-white/20"
                  style={{
                    left: `${20 + i * 15}%`,
                    top: `${20 + (i % 3) * 20}%`
                  }}
                  animate={{
                    y: [0, -20, 0],
                    opacity: [0.2, 0.8, 0.2],
                    scale: [1, 1.2, 1]
                  }}
                  transition={{
                    duration: 3 + i * 0.5,
                    repeat: Infinity,
                    delay: i * 0.8
                  }}
                />
              ))}
            </div>

            <div className="relative z-10 p-8 text-white">
              <StaggerContainer>
                <StaggerItem>
                  <motion.div
                    className="flex items-center gap-3 mb-4"
                    initial={{ opacity: 0, x: -30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <motion.div
                      className="p-3 bg-white/20 backdrop-blur-sm"
                      whileHover={{ scale: 1.1, rotate: 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Store className="h-8 w-8" />
                    </motion.div>
                    <div>
                      <h1 className="text-4xl font-bold font-heading">OKDOI E-Store</h1>
                      <div className="flex items-center gap-2 mt-1">
                        <Award className="h-4 w-4 text-yellow-300" />
                        <span className="text-blue-100 font-medium">Verified Vendors</span>
                      </div>
                    </div>
                  </motion.div>
                </StaggerItem>

                <StaggerItem>
                  <motion.p
                    className="text-xl text-blue-100 mb-6 max-w-2xl"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    Discover amazing shops and products from verified vendors across Sri Lanka
                  </motion.p>
                </StaggerItem>

                <StaggerItem>
                  {/* Enhanced Search Bar */}
                  <motion.div
                    className="max-w-2xl"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                  >
                    <div className="relative">
                      <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                      <motion.input
                        type="text"
                        placeholder="Search shops, products, or vendors..."
                        value={filters.search}
                        onChange={(e) => handleFilterChange('search', e.target.value)}
                        className="w-full pl-12 pr-32 py-4 text-gray-900 placeholder-gray-500 bg-white/95 backdrop-blur-sm border-2 border-white/20 hover:border-white/40 focus:border-white focus:ring-4 focus:ring-white/20 transition-all duration-300"
                        whileFocus={{ scale: 1.02 }}
                        transition={{ duration: 0.2 }}
                      />
                      <motion.button
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black px-6 py-3 font-bold transition-all duration-300 shadow-lg"
                        whileHover={{ scale: 1.05, boxShadow: '0 10px 25px rgba(251, 191, 36, 0.3)' }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Search className="h-5 w-5" />
                      </motion.button>
                    </div>
                  </motion.div>
                </StaggerItem>
              </StaggerContainer>
            </div>
          </PremiumCard>
        </FadeIn>

        {/* Enhanced Filters and View Controls */}
        <SlideInUp delay={0.2}>
          <PremiumCard variant="premium" className="mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center gap-4">
                <PremiumButton
                  variant="outline"
                  onClick={() => setShowFilters(!showFilters)}
                  icon={<SlidersHorizontal className="h-4 w-4" />}
                  className="flex-shrink-0"
                >
                  {showFilters ? 'Hide Filters' : 'Show Filters'}
                </PremiumButton>

                <AnimatePresence>
                  {(filters.search || filters.category_id || filters.shop_id || filters.featured || filters.min_price || filters.max_price) && (
                    <motion.button
                      onClick={clearFilters}
                      className="text-sm text-red-600 hover:text-red-700 font-medium transition-colors"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      Clear all filters
                    </motion.button>
                  )}
                </AnimatePresence>
              </div>

              <div className="flex items-center gap-4">
                <motion.div
                  className="flex items-center gap-2 text-sm text-gray-600"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  <span className="font-bold text-primary-blue">{totalProducts}</span> products found
                </motion.div>

                <motion.div
                  className="flex items-center bg-gray-100 p-1 shadow-inner"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.4 }}
                >
                  <motion.button
                    onClick={() => setViewMode('grid')}
                    className={`p-3 transition-all duration-300 ${
                      viewMode === 'grid'
                        ? 'bg-primary-blue text-white shadow-lg'
                        : 'text-gray-600 hover:text-gray-800 hover:bg-white'
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Grid className="h-4 w-4" />
                  </motion.button>
                  <motion.button
                    onClick={() => setViewMode('list')}
                    className={`p-3 transition-all duration-300 ${
                      viewMode === 'list'
                        ? 'bg-primary-blue text-white shadow-lg'
                        : 'text-gray-600 hover:text-gray-800 hover:bg-white'
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <List className="h-4 w-4" />
                  </motion.button>
                </motion.div>
              </div>
            </div>
          </PremiumCard>
        </SlideInUp>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  value={filters.category_id}
                  onChange={(e) => handleFilterChange('category_id', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Subcategory</label>
                <select
                  value={filters.subcategory_id}
                  onChange={(e) => handleFilterChange('subcategory_id', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                  disabled={!filters.category_id}
                >
                  <option value="">All Subcategories</option>
                  {filters.category_id && categories
                    .find(c => c.id === filters.category_id)
                    ?.subcategories?.map((subcategory) => (
                      <option key={subcategory.id} value={subcategory.id}>
                        {subcategory.name}
                      </option>
                    ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Min Price (Rs)</label>
                <input
                  type="number"
                  min="0"
                  placeholder="0"
                  value={filters.min_price}
                  onChange={(e) => handleFilterChange('min_price', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Max Price (Rs)</label>
                <input
                  type="number"
                  min="0"
                  placeholder="Any"
                  value={filters.max_price}
                  onChange={(e) => handleFilterChange('max_price', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Product Type</label>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="featured"
                    checked={filters.featured}
                    onChange={(e) => handleFilterChange('featured', e.target.checked)}
                    className="h-4 w-4 text-primary-blue focus:ring-primary-blue border-gray-300 rounded"
                  />
                  <label htmlFor="featured" className="ml-2 text-sm text-gray-700">
                    Featured only
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Loading State */}
        <AnimatePresence>
          {loading && (
            <motion.div
              className="flex flex-col items-center justify-center py-16"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <motion.div
                className="w-16 h-16 border-4 border-primary-blue border-t-transparent mb-4"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              />
              <motion.p
                className="text-gray-600 font-medium"
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                Loading amazing products...
              </motion.p>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Enhanced Error State */}
        <AnimatePresence>
          {error && (
            <motion.div
              className="text-center py-16"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
            >
              <motion.div
                className="text-6xl mb-4"
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 0.5 }}
              >
                😞
              </motion.div>
              <div className="text-red-600 mb-4 font-medium">{error}</div>
              <PremiumButton
                variant="primary"
                onClick={fetchProducts}
                icon={<Zap className="h-4 w-4" />}
              >
                Try Again
              </PremiumButton>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Enhanced Featured Shops Section */}
        <AnimatePresence>
          {!loading && featuredShops.length > 0 && (
            <motion.div
              className="mb-8"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <motion.div
                className="flex items-center gap-3 mb-6"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
              >
                <motion.div
                  className="p-2 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <Award className="h-5 w-5" />
                </motion.div>
                <h2 className="text-2xl font-bold font-heading text-gray-900">Featured Shops</h2>
              </motion.div>

              <StaggerContainer>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {featuredShops.map((shop, index) => (
                    <StaggerItem key={shop.id}>
                      <PremiumCard
                        variant="premium"
                        padding="none"
                        className="overflow-hidden hover-lift"
                        hover
                      >
                        <div className="aspect-w-16 aspect-h-9 bg-gray-200 relative overflow-hidden">
                          {shop.banner_url ? (
                            <motion.img
                              src={shop.banner_url}
                              alt={shop.name}
                              className="w-full h-32 object-cover"
                              whileHover={{ scale: 1.1 }}
                              transition={{ duration: 0.3 }}
                            />
                          ) : (
                            <motion.div
                              className="w-full h-32 bg-gradient-to-r from-primary-blue to-secondary-blue flex items-center justify-center"
                              whileHover={{ scale: 1.05 }}
                              transition={{ duration: 0.3 }}
                            >
                              <Store className="h-8 w-8 text-white" />
                            </motion.div>
                          )}

                          {/* Featured Badge */}
                          <motion.div
                            className="absolute top-2 right-2 bg-gradient-to-r from-yellow-400 to-yellow-500 text-black px-2 py-1 text-xs font-bold"
                            initial={{ scale: 0, rotate: -45 }}
                            animate={{ scale: 1, rotate: 0 }}
                            transition={{ delay: 0.5 + index * 0.1 }}
                          >
                            ⭐ Featured
                          </motion.div>
                        </div>

                        <div className="p-4">
                          <Link href={`/shop/${shop.slug}`} className="font-bold text-gray-900 hover:text-primary-blue transition-colors block mb-2">
                            {shop.name}
                          </Link>
                          <p className="text-sm text-gray-600 mb-3 line-clamp-2">{shop.description}</p>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center text-sm text-gray-500">
                              <Star className="h-4 w-4 text-yellow-400 mr-1 fill-current" />
                              <span className="font-medium">{shop.rating.toFixed(1)}</span>
                              <span className="ml-1">({shop.total_reviews})</span>
                            </div>
                            <motion.div
                              className="text-xs bg-green-100 text-green-800 px-2 py-1 font-medium"
                              whileHover={{ scale: 1.05 }}
                            >
                              Verified ✓
                            </motion.div>
                          </div>
                        </div>
                      </PremiumCard>
                    </StaggerItem>
                  ))}
                </div>
              </StaggerContainer>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Enhanced Products Grid/List */}
        <AnimatePresence>
          {!loading && !error && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              {products.length === 0 ? (
                <motion.div
                  className="text-center py-16"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <motion.div
                    animate={{ rotate: [0, 10, -10, 0] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <Package className="h-20 w-20 text-gray-400 mx-auto mb-4" />
                  </motion.div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">No products found</h3>
                  <p className="text-gray-600 mb-6">Try adjusting your search criteria or explore different categories</p>
                  <PremiumButton
                    variant="outline"
                    onClick={clearFilters}
                    icon={<Sparkles className="h-4 w-4" />}
                  >
                    Clear Filters
                  </PremiumButton>
                </motion.div>
              ) : (
                <StaggerContainer>
                  <div className={viewMode === 'grid'
                    ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                    : 'space-y-4'
                  }>
                    {products.map((product, index) => (
                      <StaggerItem key={product.id}>
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <ProductCard product={product} viewMode={viewMode} />
                        </motion.div>
                      </StaggerItem>
                    ))}
                  </div>
                </StaggerContainer>
              )}

              {/* Enhanced Pagination */}
              {totalPages > 1 && (
                <motion.div
                  className="flex items-center justify-center gap-2 mt-12"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 }}
                >
                  <PremiumButton
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    size="sm"
                  >
                    Previous
                  </PremiumButton>

                  <div className="flex items-center gap-1">
                    {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                      let page = i + 1
                      if (totalPages > 5) {
                        if (currentPage > 3) {
                          page = currentPage - 2 + i
                        }
                        if (currentPage > totalPages - 2) {
                          page = totalPages - 4 + i
                        }
                      }
                      return (
                        <motion.button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`px-4 py-2 text-sm font-medium transition-all duration-300 ${
                            currentPage === page
                              ? 'bg-primary-blue text-white shadow-lg'
                              : 'text-gray-700 hover:bg-gray-100'
                          }`}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          {page}
                        </motion.button>
                      )
                    })}
                  </div>

                  <PremiumButton
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    size="sm"
                  >
                    Next
                  </PremiumButton>
                </motion.div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </main>

      <Footer />

      {/* Floating Cart Button */}
      <FloatingCartButton />
    </div>
  )
}

// The ProductCard component is now imported from @/components/products/ProductCard
