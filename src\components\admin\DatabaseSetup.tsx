'use client'

import React, { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Co<PERSON>, ExternalLink, CheckCircle } from 'lucide-react'

const KYC_SETUP_SQL = `-- KYC System Tables Creation Script
-- Run this manually in Supabase SQL Editor

-- Step 1: Add KYC columns to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_status varchar(20) DEFAULT 'not_submitted' 
CHECK (kyc_status IN ('not_submitted', 'pending', 'approved', 'rejected'));

ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_submitted_at timestamp with time zone;
ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_approved_at timestamp with time zone;

-- Step 2: Create KYC submissions table
CREATE TABLE IF NOT EXISTS kyc_submissions (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Document URLs (stored in Supabase Storage)
    id_document_front_url text NOT NULL,
    id_document_back_url text NOT NULL,
    selfie_photo_url text NOT NULL,
    address_proof_url text NOT NULL,
    
    -- Document metadata
    id_document_type varchar(50) NOT NULL CHECK (id_document_type IN ('national_id', 'passport', 'driving_license')),
    id_document_number varchar(100),
    
    -- Personal information for verification
    full_name varchar(255) NOT NULL,
    date_of_birth date,
    address text NOT NULL,
    
    -- Submission status and tracking
    status varchar(20) DEFAULT 'pending' CHECK (status IN ('pending', 'under_review', 'approved', 'rejected')),
    submission_notes text,
    
    -- Admin review fields
    reviewed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
    reviewed_at timestamp with time zone,
    rejection_reason text,
    admin_notes text,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    -- Ensure one active submission per user
    UNIQUE(user_id)
);

-- Step 3: Create other KYC tables and setup
-- (See full script in create_kyc_tables.sql file)`

export default function DatabaseSetup() {
  const [copied, setCopied] = useState(false)

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(KYC_SETUP_SQL)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  const openSupabase = () => {
    window.open('https://supabase.com/dashboard/project/vnmydqbwjjufnxngpnqo/sql/new', '_blank')
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span>KYC Database Setup Required</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertDescription>
              The KYC system requires database tables to be created. Please run the SQL script below in your Supabase SQL Editor.
            </AlertDescription>
          </Alert>

          <div className="space-y-4">
            <div className="flex gap-2">
              <Button onClick={copyToClipboard} variant="outline" size="sm">
                {copied ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                {copied ? 'Copied!' : 'Copy SQL'}
              </Button>
              <Button onClick={openSupabase} variant="outline" size="sm">
                <ExternalLink className="w-4 h-4" />
                Open Supabase SQL Editor
              </Button>
            </div>

            <div className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-x-auto">
              <pre className="text-sm whitespace-pre-wrap">{KYC_SETUP_SQL}</pre>
            </div>

            <Alert>
              <AlertDescription>
                <strong>Instructions:</strong>
                <ol className="list-decimal list-inside mt-2 space-y-1">
                  <li>Copy the SQL script above</li>
                  <li>Open the Supabase SQL Editor using the button above</li>
                  <li>Paste and run the script</li>
                  <li>Refresh this page after running the script</li>
                </ol>
              </AlertDescription>
            </Alert>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
