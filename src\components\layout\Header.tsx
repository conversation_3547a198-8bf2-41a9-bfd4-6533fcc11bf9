'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { Menu, X, User, Plus, LayoutDashboard, LogOut, Shield, MessageCircle, Store, ShoppingCart, Globe, ChevronDown } from 'lucide-react'
import Button from '@/components/ui/Button'
import PremiumButton from '@/components/ui/PremiumButton'
import { HeaderLogo } from '@/components/ui/Logo'
import { DropdownTransition } from '@/components/ui/animations'
import { useAuth } from '@/contexts/AuthContext'
import { useCart } from '@/contexts/CartContext'
import { AuthService } from '@/lib/auth'
import { useUnreadMessages } from '@/hooks/useUnreadMessages'
import CartSidebar from '@/components/cart/CartSidebar'

export default function Header() {
  const pathname = usePathname()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showLanguageMenu, setShowLanguageMenu] = useState(false)
  const [showCartSidebar, setShowCartSidebar] = useState(false)
  const [sessionValid, setSessionValid] = useState(true)
  const [isScrolled, setIsScrolled] = useState(false)
  const [lastScrollY, setLastScrollY] = useState(0)
  const [currentLanguage, setCurrentLanguage] = useState('en')
  const { user, signOut, loading } = useAuth()
  const { cartCount } = useCart()
  const { unreadCount } = useUnreadMessages()

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'si', name: 'සිංහල', flag: '🇱🇰' }
  ]

  // Check if current page is e-store related
  const isEStoreRelated = pathname?.startsWith('/e-store') ||
                         pathname?.startsWith('/product/') ||
                         pathname?.startsWith('/shop/') ||
                         pathname?.startsWith('/checkout')

  // Handle scroll behavior for sticky header
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY

      // Set scrolled state for styling
      setIsScrolled(currentScrollY > 10)

      // Close dropdowns when scrolling
      if (currentScrollY > lastScrollY) {
        setShowUserMenu(false)
        setIsMenuOpen(false)
      }

      setLastScrollY(currentScrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [lastScrollY])

  // Handle click outside to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.user-menu') && !target.closest('.mobile-menu') && !target.closest('.language-menu')) {
        setShowUserMenu(false)
        setShowLanguageMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Check session validity periodically
  useEffect(() => {
    const checkSession = async () => {
      if (user) {
        const isValid = await AuthService.isSessionValid()
        setSessionValid(isValid)

        if (!isValid) {
          console.log('Header: Invalid session detected, signing out...')
          try {
            await signOut()
          } catch (error) {
            console.error('Header: Error signing out:', error)
          }
        }
      }
    }

    // Check immediately
    checkSession()

    // Check every 5 minutes
    const interval = setInterval(checkSession, 5 * 60 * 1000)

    return () => clearInterval(interval)
  }, [user, signOut])

  const handleSignOut = async () => {
    try {
      await signOut()
      setShowUserMenu(false)
      window.location.href = '/'
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  return (
    <motion.header
      className={`bg-white/95 backdrop-blur-md fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? 'shadow-xl border-b border-gray-100' : 'shadow-sm'
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-24">
          {/* Left Side - Logo and Navigation */}
          <div className="flex items-center space-x-8">
            {/* Logo */}
            <motion.div
              className="flex-shrink-0"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <HeaderLogo />
            </motion.div>

            {/* Navigation Links - Desktop */}
            <div className="hidden md:flex items-center space-x-6">
              <motion.div
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <Link
                  href="/ads"
                  className="text-gray-700 hover:text-primary-blue font-medium transition-all duration-300 px-3 py-2 hover:bg-primary-blue/5"
                >
                  All Ads
                </Link>
              </motion.div>

              {/* Enhanced Language Changer */}
              <div className="relative language-menu">
                <motion.button
                  onClick={() => setShowLanguageMenu(!showLanguageMenu)}
                  className="flex items-center text-gray-700 hover:text-primary-blue font-medium transition-all duration-300 px-3 py-2 hover:bg-primary-blue/5 gap-2"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Globe className="h-4 w-4" />
                  <span>{languages.find(lang => lang.code === currentLanguage)?.flag}</span>
                  <span className="hidden lg:block">{languages.find(lang => lang.code === currentLanguage)?.name}</span>
                  <motion.div
                    animate={{ rotate: showLanguageMenu ? 180 : 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronDown className="h-4 w-4" />
                  </motion.div>
                </motion.button>

                <DropdownTransition isOpen={showLanguageMenu} origin="top">
                  <div className="absolute top-full right-0 mt-2 w-48 bg-white shadow-xl border border-gray-100 overflow-hidden z-50">
                    {languages.map((language) => (
                      <motion.button
                        key={language.code}
                        onClick={() => {
                          setCurrentLanguage(language.code)
                          setShowLanguageMenu(false)
                        }}
                        className={`w-full flex items-center px-4 py-3 text-sm transition-colors ${
                          currentLanguage === language.code
                            ? 'bg-primary-blue text-white'
                            : 'text-gray-700 hover:bg-gray-50'
                        }`}
                        whileHover={{ backgroundColor: currentLanguage === language.code ? undefined : '#f9fafb' }}
                      >
                        <span className="mr-3 text-lg">{language.flag}</span>
                        <span className="font-medium">{language.name}</span>
                      </motion.button>
                    ))}
                  </div>
                </DropdownTransition>
              </div>
            </div>
          </div>

          {/* Right Side Actions */}
          <div className="hidden md:flex items-center space-x-4">

            {loading ? (
              <motion.div
                className="w-6 h-6 border-2 border-primary-blue border-t-transparent"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
              />
            ) : user && sessionValid ? (
              <>
                {/* E-Store Link */}
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Link
                    href="/e-store"
                    className="flex items-center p-3 text-gray-700 hover:text-primary-blue font-medium transition-all duration-300 hover:bg-primary-blue/5"
                    title="E-Store"
                  >
                    <Store className="h-5 w-5" />
                  </Link>
                </motion.div>

                {/* Chat Icon with Notification */}
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <Link href="/dashboard/chats" className="relative p-3 text-gray-700 hover:text-primary-blue transition-all duration-300 hover:bg-primary-blue/5 block">
                    <MessageCircle className="h-6 w-6" />
                    <AnimatePresence>
                      {unreadCount > 0 && (
                        <motion.div
                          className="absolute -top-1 -right-1 flex items-center justify-center"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          exit={{ scale: 0 }}
                          transition={{ duration: 0.2 }}
                        >
                          <motion.div
                            className="w-6 h-6 bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center shadow-lg"
                            animate={{ scale: [1, 1.1, 1] }}
                            transition={{ duration: 2, repeat: Infinity }}
                          >
                            <span className="text-xs font-bold text-white">
                              {unreadCount > 99 ? '99+' : unreadCount}
                            </span>
                          </motion.div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </Link>
                </motion.div>

                <div className="relative user-menu">
                  <motion.button
                    onClick={() => setShowUserMenu(!showUserMenu)}
                    className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 hover:text-primary-blue transition-all duration-300 hover:bg-primary-blue/5 gap-3"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <motion.div
                      className="w-10 h-10 overflow-hidden"
                      whileHover={{ scale: 1.1 }}
                      transition={{ duration: 0.2 }}
                    >
                      {user.avatar_url ? (
                        <img
                          src={user.avatar_url}
                          alt="Profile picture"
                          className="w-full h-full object-cover object-center"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-primary-blue to-secondary-blue flex items-center justify-center">
                          <span className="text-sm font-bold text-white">
                            {user.full_name?.charAt(0) || user.email?.charAt(0) || 'U'}
                          </span>
                        </div>
                      )}
                    </motion.div>
                    <div className="hidden lg:block text-left">
                      <div className="font-semibold">{user.full_name || 'User'}</div>
                      <div className="text-xs text-gray-500">{user.email}</div>
                    </div>
                    <motion.div
                      animate={{ rotate: showUserMenu ? 180 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronDown className="h-4 w-4" />
                    </motion.div>
                  </motion.button>

                  <DropdownTransition isOpen={showUserMenu} origin="top">
                    <div className="absolute right-0 mt-2 w-56 bg-white shadow-xl border border-gray-100 overflow-hidden z-50">
                      <div className="py-2">
                        <motion.div
                          whileHover={{ backgroundColor: '#f9fafb' }}
                          transition={{ duration: 0.2 }}
                        >
                          <Link
                            href="/dashboard"
                            className="flex items-center px-4 py-3 text-sm text-gray-700 hover:text-primary-blue transition-colors"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <LayoutDashboard className="h-5 w-5 mr-3 text-gray-400" />
                            <div>
                              <div className="font-medium">Dashboard</div>
                              <div className="text-xs text-gray-500">Manage your account</div>
                            </div>
                          </Link>
                        </motion.div>
                        {(user.role === 'admin' || user.is_super_admin) && (
                          <motion.div
                            whileHover={{ backgroundColor: '#f9fafb' }}
                            transition={{ duration: 0.2 }}
                          >
                            <Link
                              href="/admin"
                              className="flex items-center px-4 py-3 text-sm text-primary-blue hover:text-primary-600 transition-colors"
                              onClick={() => setShowUserMenu(false)}
                            >
                              <Shield className="h-5 w-5 mr-3 text-primary-blue" />
                              <div>
                                <div className="font-medium">Admin Dashboard</div>
                                <div className="text-xs text-primary-blue/60">System management</div>
                              </div>
                            </Link>
                          </motion.div>
                        )}
                        <div className="border-t border-gray-100 my-2"></div>
                        <motion.button
                          onClick={handleSignOut}
                          className="flex items-center w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors"
                          whileHover={{ backgroundColor: '#fef2f2' }}
                          transition={{ duration: 0.2 }}
                        >
                          <LogOut className="h-5 w-5 mr-3" />
                          <div className="text-left">
                            <div className="font-medium">Sign Out</div>
                            <div className="text-xs text-red-400">End your session</div>
                          </div>
                        </motion.button>
                      </div>
                    </div>
                  </DropdownTransition>
                </div>

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link href="/post-ad">
                    <PremiumButton
                      variant="gradient"
                      className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white font-bold shadow-lg"
                      icon={<Plus className="h-5 w-5" />}
                      iconPosition="left"
                    >
                      Post Ad
                    </PremiumButton>
                  </Link>
                </motion.div>
              </>
            ) : (
              <>
                {/* E-Store Link */}
                <Link
                  href="/e-store"
                  className="flex items-center text-gray-700 hover:text-pink-500 font-medium transition-colors duration-200"
                  title="E-Store"
                >
                  <Store className="h-5 w-5" />
                </Link>

                {/* Cart Button - Only show on e-store related pages */}
                {isEStoreRelated && (
                  <button
                    onClick={() => setShowCartSidebar(true)}
                    className="relative flex items-center text-gray-700 hover:text-pink-500 transition-colors duration-200"
                    title="Shopping Cart"
                  >
                    <ShoppingCart className="h-5 w-5" />
                    {cartCount > 0 && (
                      <span className="absolute -top-2 -right-2 bg-pink-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                        {cartCount > 99 ? '99+' : cartCount}
                      </span>
                    )}
                  </button>
                )}

                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link href="/auth/signin">
                    <PremiumButton variant="outline" size="sm">
                      Sign In
                    </PremiumButton>
                  </Link>
                </motion.div>
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link href="/post-ad">
                    <PremiumButton
                      variant="gradient"
                      size="sm"
                      className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white font-bold shadow-lg"
                      icon={<Plus className="h-5 w-5" />}
                      iconPosition="left"
                    >
                      Post Ad
                    </PremiumButton>
                  </Link>
                </motion.div>
              </>
            )}
          </div>

          {/* Enhanced Mobile menu button */}
          <div className="md:hidden mobile-menu">
            <motion.button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-primary-blue p-3 transition-all duration-300 hover:bg-primary-blue/5"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <AnimatePresence mode="wait">
                {isMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <X className="h-6 w-6" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ rotate: 90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Menu className="h-6 w-6" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          </div>
        </div>

        {/* Enhanced Mobile Navigation */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              className="md:hidden mobile-menu border-t border-gray-200 bg-white/95 backdrop-blur-md"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }}
            >
              <div className="py-4 px-4 space-y-2">
                <motion.div
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.1 }}
                >
                  <Link
                    href="/ads"
                    className="flex items-center text-gray-700 hover:text-primary-blue hover:bg-primary-blue/5 px-4 py-3 text-sm font-medium transition-all duration-300"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    All Ads
                  </Link>
                </motion.div>

                <motion.div
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  <Link
                    href="/e-store"
                    className="flex items-center text-gray-700 hover:text-primary-blue hover:bg-primary-blue/5 px-4 py-3 text-sm font-medium transition-all duration-300"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Store className="h-5 w-5 mr-3" />
                    E-Store
                  </Link>
                </motion.div>

                {/* Enhanced Language Changer - Mobile */}
                <motion.div
                  className="px-4 py-2"
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.3 }}
                >
                  <div className="flex items-center gap-3 p-3 bg-gray-50 hover:bg-gray-100 transition-colors">
                    <Globe className="h-5 w-5 text-gray-500" />
                    <select
                      className="flex-1 bg-transparent text-gray-700 font-medium focus:outline-none cursor-pointer"
                      value={currentLanguage}
                      onChange={(e) => setCurrentLanguage(e.target.value)}
                    >
                      {languages.map((lang) => (
                        <option key={lang.code} value={lang.code}>
                          {lang.flag} {lang.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </motion.div>
                <div className="flex flex-col space-y-3 px-4 pt-4 border-t border-gray-200">
                  {user ? (
                    <>
                      <motion.div
                        initial={{ x: -20, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ delay: 0.4 }}
                      >
                        <Link href="/dashboard" onClick={() => setIsMenuOpen(false)}>
                          <PremiumButton variant="outline" size="sm" fullWidth>
                            <LayoutDashboard className="h-4 w-4 mr-2" />
                            Dashboard
                          </PremiumButton>
                        </Link>
                      </motion.div>

                      <motion.div
                        initial={{ x: -20, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ delay: 0.5 }}
                      >
                        <Link href="/dashboard/chats" className="relative" onClick={() => setIsMenuOpen(false)}>
                          <PremiumButton variant="outline" size="sm" fullWidth>
                            <MessageCircle className="h-4 w-4 mr-2" />
                            Chats
                            <AnimatePresence>
                              {unreadCount > 0 && (
                                <motion.span
                                  className="ml-auto bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold px-2 py-1 min-w-[20px] h-5 flex items-center justify-center"
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  exit={{ scale: 0 }}
                                >
                                  {unreadCount > 99 ? '99+' : unreadCount}
                                </motion.span>
                              )}
                            </AnimatePresence>
                          </PremiumButton>
                        </Link>
                      </motion.div>

                      <motion.div
                        initial={{ x: -20, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ delay: 0.6 }}
                      >
                        <Link href="/post-ad" onClick={() => setIsMenuOpen(false)}>
                          <PremiumButton
                            variant="gradient"
                            size="sm"
                            fullWidth
                            className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white font-bold shadow-lg"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Post Ad
                          </PremiumButton>
                        </Link>
                      </motion.div>
                    </>
                  ) : (
                    <>
                      <motion.div
                        initial={{ x: -20, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ delay: 0.4 }}
                      >
                        <Link href="/auth/signin" onClick={() => setIsMenuOpen(false)}>
                          <PremiumButton variant="outline" size="sm" fullWidth>
                            <User className="h-4 w-4 mr-2" />
                            Sign In
                          </PremiumButton>
                        </Link>
                      </motion.div>

                      <motion.div
                        initial={{ x: -20, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ delay: 0.5 }}
                      >
                        <Link href="/auth/signup" onClick={() => setIsMenuOpen(false)}>
                          <PremiumButton variant="secondary" size="sm" fullWidth>
                            Sign Up
                          </PremiumButton>
                        </Link>
                      </motion.div>

                      <motion.div
                        initial={{ x: -20, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        transition={{ delay: 0.6 }}
                      >
                        <Link href="/post-ad" onClick={() => setIsMenuOpen(false)}>
                          <PremiumButton
                            variant="gradient"
                            size="sm"
                            fullWidth
                            className="bg-gradient-to-r from-pink-500 to-pink-600 hover:from-pink-600 hover:to-pink-700 text-white font-bold shadow-lg"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Post Ad
                          </PremiumButton>
                        </Link>
                      </motion.div>
                    </>
                  )}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Cart Sidebar - Only render on e-store related pages */}
      {isEStoreRelated && (
        <CartSidebar
          isOpen={showCartSidebar}
          onClose={() => setShowCartSidebar(false)}
        />
      )}
    </motion.header>
  )
}
