'use client'

import React from 'react'
import { motion } from 'framer-motion'
import {
  Shield,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  FileText
} from 'lucide-react'

export type KYCStatus = 'not_submitted' | 'pending' | 'under_review' | 'approved' | 'rejected'

interface KYCStatusBadgeProps {
  status: KYCStatus
  size?: 'sm' | 'md' | 'lg'
  showIcon?: boolean
  showText?: boolean
  className?: string
}

const statusConfig = {
  not_submitted: {
    label: 'Not Submitted',
    icon: FileText,
    bgColor: 'bg-gray-100',
    textColor: 'text-gray-700',
    iconColor: 'text-gray-500',
    borderColor: 'border-gray-200'
  },
  pending: {
    label: 'Pending Review',
    icon: Clock,
    bgColor: 'bg-yellow-100',
    textColor: 'text-yellow-800',
    iconColor: 'text-yellow-600',
    borderColor: 'border-yellow-200'
  },
  under_review: {
    label: 'Under Review',
    icon: Alert<PERSON>rian<PERSON>,
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-800',
    iconColor: 'text-blue-600',
    borderColor: 'border-blue-200'
  },
  approved: {
    label: 'Verified',
    icon: CheckCircle,
    bgColor: 'bg-green-100',
    textColor: 'text-green-800',
    iconColor: 'text-green-600',
    borderColor: 'border-green-200'
  },
  rejected: {
    label: 'Rejected',
    icon: XCircle,
    bgColor: 'bg-red-100',
    textColor: 'text-red-800',
    iconColor: 'text-red-600',
    borderColor: 'border-red-200'
  }
}

const sizeConfig = {
  sm: {
    padding: 'px-2 py-1',
    text: 'text-xs',
    icon: 'h-3 w-3'
  },
  md: {
    padding: 'px-3 py-1.5',
    text: 'text-sm',
    icon: 'h-4 w-4'
  },
  lg: {
    padding: 'px-4 py-2',
    text: 'text-base',
    icon: 'h-5 w-5'
  }
}

export default function KYCStatusBadge({
  status,
  size = 'md',
  showIcon = true,
  showText = true,
  className = ''
}: KYCStatusBadgeProps) {
  const config = statusConfig[status]
  const sizeStyles = sizeConfig[size]
  const IconComponent = config.icon

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`
        inline-flex items-center font-medium border
        ${config.bgColor} ${config.textColor} ${config.borderColor}
        ${sizeStyles.padding} ${sizeStyles.text}
        rounded-full
        ${className}
      `}
    >
      {showIcon && (
        <IconComponent className={`${sizeStyles.icon} ${config.iconColor} ${showText ? 'mr-1.5' : ''}`} />
      )}
      {showText && config.label}
    </motion.div>
  )
}

// KYC Progress Indicator Component
interface KYCProgressProps {
  currentStatus: KYCStatus
  className?: string
}

const progressSteps: { status: KYCStatus; label: string }[] = [
  { status: 'not_submitted', label: 'Submit Documents' },
  { status: 'pending', label: 'Under Review' },
  { status: 'approved', label: 'Verified' }
]

export function KYCProgress({ currentStatus, className = '' }: KYCProgressProps) {
  const getCurrentStep = () => {
    switch (currentStatus) {
      case 'not_submitted':
        return 0
      case 'pending':
      case 'under_review':
        return 1
      case 'approved':
        return 2
      case 'rejected':
        return 0 // Reset to start for rejected
      default:
        return 0
    }
  }

  const currentStep = getCurrentStep()
  const isRejected = currentStatus === 'rejected'

  return (
    <div className={`w-full ${className}`}>
      <div className="flex items-center justify-between">
        {progressSteps.map((step, index) => {
          const isActive = index === currentStep
          const isCompleted = index < currentStep
          const isDisabled = isRejected && index > 0

          return (
            <div key={step.status} className="flex items-center">
              {/* Step Circle */}
              <div className="relative">
                <motion.div
                  initial={{ scale: 0.8 }}
                  animate={{ scale: 1 }}
                  className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                    ${isCompleted 
                      ? 'bg-green-600 text-white' 
                      : isActive 
                        ? 'bg-primary-blue text-white' 
                        : isDisabled
                          ? 'bg-red-100 text-red-600 border-2 border-red-200'
                          : 'bg-gray-200 text-gray-600'
                    }
                  `}
                >
                  {isCompleted ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : isDisabled ? (
                    <XCircle className="h-4 w-4" />
                  ) : (
                    index + 1
                  )}
                </motion.div>
              </div>

              {/* Step Label */}
              <div className="ml-3">
                <p className={`text-sm font-medium ${
                  isActive ? 'text-primary-blue' : 
                  isCompleted ? 'text-green-600' : 
                  isDisabled ? 'text-red-600' : 
                  'text-gray-500'
                }`}>
                  {step.label}
                </p>
              </div>

              {/* Connector Line */}
              {index < progressSteps.length - 1 && (
                <div className={`
                  flex-1 h-0.5 mx-4
                  ${isCompleted ? 'bg-green-600' : 'bg-gray-200'}
                `} />
              )}
            </div>
          )
        })}
      </div>

      {/* Rejection Message */}
      {isRejected && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg"
        >
          <div className="flex items-center">
            <XCircle className="h-5 w-5 text-red-600 mr-2" />
            <p className="text-sm text-red-800 font-medium">
              Your KYC submission was rejected. Please review the feedback and resubmit.
            </p>
          </div>
        </motion.div>
      )}
    </div>
  )
}

// KYC Verification Badge for Profile/Header
interface KYCVerificationBadgeProps {
  isVerified: boolean
  size?: 'sm' | 'md' | 'lg'
  showTooltip?: boolean
  className?: string
}

export function KYCVerificationBadge({
  isVerified,
  size = 'md',
  showTooltip = true,
  className = ''
}: KYCVerificationBadgeProps) {
  const sizeStyles = sizeConfig[size]

  if (!isVerified) return null

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`
        inline-flex items-center bg-green-100 text-green-800 border border-green-200
        ${sizeStyles.padding} ${sizeStyles.text}
        rounded-full font-medium
        ${showTooltip ? 'cursor-help' : ''}
        ${className}
      `}
      title={showTooltip ? 'KYC Verified User' : undefined}
    >
      <Shield className={`${sizeStyles.icon} text-green-600 mr-1`} />
      Verified
    </motion.div>
  )
}

// KYC Status Card Component
interface KYCStatusCardProps {
  status: KYCStatus
  submittedAt?: string
  approvedAt?: string
  rejectionReason?: string
  className?: string
}

export function KYCStatusCard({
  status,
  submittedAt,
  approvedAt,
  rejectionReason,
  className = ''
}: KYCStatusCardProps) {
  const config = statusConfig[status]

  const getStatusMessage = () => {
    switch (status) {
      case 'not_submitted':
        return 'Complete your KYC verification to unlock all features including wallet withdrawals, P2P transfers, and shop creation.'
      case 'pending':
        return 'Your KYC documents have been submitted and are pending review. We will notify you once the review is complete.'
      case 'under_review':
        return 'Our team is currently reviewing your KYC documents. This process typically takes 1-3 business days.'
      case 'approved':
        return 'Congratulations! Your identity has been verified. You now have access to all platform features.'
      case 'rejected':
        return 'Your KYC submission was rejected. Please review the feedback below and resubmit your documents.'
      default:
        return ''
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`
        p-6 border rounded-lg
        ${config.bgColor} ${config.borderColor}
        ${className}
      `}
    >
      <div className="flex items-start space-x-4">
        <div className={`p-2 rounded-lg ${config.bgColor} border ${config.borderColor}`}>
          <config.icon className={`h-6 w-6 ${config.iconColor}`} />
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <h3 className={`text-lg font-semibold ${config.textColor}`}>
              KYC Status: {config.label}
            </h3>
            <KYCStatusBadge status={status} size="sm" />
          </div>
          <p className="text-gray-700 mb-4">
            {getStatusMessage()}
          </p>

          {/* Timestamps */}
          <div className="space-y-2 text-sm text-gray-600">
            {submittedAt && (
              <p>
                <span className="font-medium">Submitted:</span>{' '}
                {new Date(submittedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            )}
            {approvedAt && (
              <p>
                <span className="font-medium">Approved:</span>{' '}
                {new Date(approvedAt).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            )}
          </div>

          {/* Rejection Reason */}
          {status === 'rejected' && rejectionReason && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm font-medium text-red-800 mb-1">Rejection Reason:</p>
              <p className="text-sm text-red-700">{rejectionReason}</p>
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
}
