'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>, Sparkles, TrendingUp } from 'lucide-react'

interface PremiumSearchBoxProps {
  className?: string
}

const PremiumSearchBox: React.FC<PremiumSearchBoxProps> = ({ className = '' }) => {
  const [searchQuery, setSearchQuery] = useState('')
  const [isFocused, setIsFocused] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)

  const popularSearches = [
    'Mobile Phones', 'Cars', 'Houses', 'Jobs', 'Electronics', 'Fashion'
  ]

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      window.location.href = `/search?q=${encodeURIComponent(searchQuery.trim())}`
    }
  }

  return (
    <motion.div
      className={`w-full max-w-6xl mx-auto ${className}`}
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.5 }}
    >
      <form onSubmit={handleSearch} className="relative">
        {/* Enhanced Glass Container */}
        <motion.div
          className="relative glass-card backdrop-blur-2xl shadow-2xl border border-white/20 overflow-hidden rounded-2xl"
          whileHover={{ scale: 1.02, boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)' }}
          whileFocus={{ scale: 1.02 }}
          transition={{ duration: 0.3 }}
        >
          {/* Animated Background Gradient */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-primary-blue/10 via-secondary-blue/10 to-primary-blue/10"
            animate={{
              background: [
                'linear-gradient(to right, rgba(0, 85, 159, 0.1), rgba(0, 124, 179, 0.1), rgba(0, 85, 159, 0.1))',
                'linear-gradient(to right, rgba(0, 124, 179, 0.1), rgba(0, 85, 159, 0.1), rgba(0, 124, 179, 0.1))',
                'linear-gradient(to right, rgba(0, 85, 159, 0.1), rgba(0, 124, 179, 0.1), rgba(0, 85, 159, 0.1))'
              ]
            }}
            transition={{ duration: 4, repeat: Infinity, ease: 'easeInOut' }}
          />

          {/* Sparkle Effects */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute"
                style={{
                  left: `${25 + i * 25}%`,
                  top: `${20 + i * 15}%`
                }}
                animate={{
                  scale: [0, 1, 0],
                  rotate: [0, 180, 360],
                  opacity: [0, 0.6, 0]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.5
                }}
              >
                <Sparkles className="h-4 w-4 text-primary-blue/30" />
              </motion.div>
            ))}
          </div>

          {/* Main Search Container */}
          <div className="relative z-10 p-8">
            <div className="flex items-center space-x-4">
              {/* Search Icon */}
              <motion.div 
                className="flex-shrink-0"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.2 }}
              >
                <div className="w-12 h-12 bg-gradient-to-br from-primary-blue to-secondary-blue rounded-full flex items-center justify-center shadow-lg">
                  <Search className="h-6 w-6 text-white" />
                </div>
              </motion.div>

              {/* Search Input */}
              <div className="flex-1 relative">
                <motion.input
                  type="text"
                  placeholder="What are you looking for today?"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onFocus={() => {
                    setIsFocused(true)
                    setShowSuggestions(true)
                  }}
                  onBlur={() => {
                    setIsFocused(false)
                    setTimeout(() => setShowSuggestions(false), 200)
                  }}
                  className="w-full text-xl font-medium text-gray-800 placeholder-gray-500 bg-transparent border-none outline-none focus:ring-0 py-2"
                  whileFocus={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                />
                
                {/* Input Underline Effect */}
                <motion.div 
                  className="absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-primary-blue to-secondary-blue"
                  initial={{ width: 0 }}
                  animate={{ width: isFocused ? '100%' : 0 }}
                  transition={{ duration: 0.3 }}
                />
              </div>

              {/* Search Button */}
              <motion.div 
                className="flex-shrink-0"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <button
                  type="submit"
                  className="px-10 py-4 bg-gradient-to-r from-primary-blue to-secondary-blue text-white font-bold rounded-xl hover:from-primary-600 hover:to-secondary-600 transition-all duration-300 shadow-lg hover:shadow-xl text-lg"
                >
                  Search
                </button>
              </motion.div>
            </div>

            {/* Popular Searches */}
            <AnimatePresence>
              {showSuggestions && (
                <motion.div 
                  className="mt-4 pt-4 border-t border-white/20"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="flex items-center mb-3">
                    <TrendingUp className="h-4 w-4 text-primary-blue mr-2" />
                    <span className="text-sm font-medium text-gray-600">Popular Searches</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {popularSearches.map((search, index) => (
                      <motion.button
                        key={index}
                        type="button"
                        onClick={() => {
                          setSearchQuery(search)
                          setShowSuggestions(false)
                        }}
                        className="px-3 py-1.5 text-sm bg-white/80 hover:bg-primary-blue hover:text-white text-gray-700 rounded-full border border-gray-200 transition-all duration-200 hover:shadow-md"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.1 }}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {search}
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      </form>

      {/* Search Stats */}
      <motion.div 
        className="mt-4 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1 }}
      >
        <p className="text-sm text-white/80">
          <motion.span 
            className="font-semibold"
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            50,000+
          </motion.span> active listings • 
          <motion.span 
            className="font-semibold ml-1"
            animate={{ scale: [1, 1.1, 1] }}
            transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
          >
            1,000+
          </motion.span> new ads daily
        </p>
      </motion.div>
    </motion.div>
  )
}

export default PremiumSearchBox
